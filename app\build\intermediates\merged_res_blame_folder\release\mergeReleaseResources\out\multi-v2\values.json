{"logs": [{"outputFile": "com.trustmarket.trust.app-mergeReleaseResources-66:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cc2713ce10801f2cf096c9628fdbc514\\transformed\\navigation-common-2.8.5\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "506,519,525,531,540", "startColumns": "4,4,4,4,4", "startOffsets": "28246,28885,29129,29376,29739", "endLines": "518,524,530,533,544", "endColumns": "24,24,24,24,24", "endOffsets": "28880,29124,29371,29504,29916"}}, {"source": "C:\\Users\\<USER>\\Desktop\\trust\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "82", "endOffsets": "134"}, "to": {"startLines": "331", "startColumns": "4", "startOffsets": "22360", "endColumns": "81", "endOffsets": "22437"}}, {"source": "C:\\Users\\<USER>\\Desktop\\trust\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1,23,29,25,37,11,36,14,13,31,45,43,41,44,42,40,28,30,12,15,35,24,34,22,16,7,5,4,6,8,17,21,20", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16,1018,1231,1121,1545,463,1504,637,569,1324,1937,1797,1656,1868,1732,1609,1184,1285,514,694,1438,1068,1389,979,752,282,143,88,212,370,799,924,868", "endColumns": "49,49,53,39,40,50,40,56,67,38,64,70,75,68,64,46,46,38,54,57,65,52,48,38,46,87,68,54,69,73,49,54,55", "endOffsets": "61,1063,1280,1156,1581,509,1540,689,632,1358,1997,1863,1727,1932,1792,1651,1226,1319,564,747,1499,1116,1433,1013,794,365,207,138,277,439,844,974,919"}, "to": {"startLines": "133,134,135,136,144,145,167,168,169,173,178,179,180,181,182,183,190,193,256,258,262,263,264,266,267,268,269,270,271,272,273,283,284", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8217,8267,8317,8371,8922,8963,11534,11575,11632,12011,12323,12388,12459,12535,12604,12669,13341,13500,18042,18151,18383,18449,18502,18597,18636,18683,18771,18840,18895,18965,19039,19572,19627", "endColumns": "49,49,53,39,40,50,40,56,67,38,64,70,75,68,64,46,46,38,54,57,65,52,48,38,46,87,68,54,69,73,49,54,55", "endOffsets": "8262,8312,8366,8406,8958,9009,11570,11627,11695,12045,12383,12454,12530,12599,12664,12711,13383,13534,18092,18204,18444,18497,18546,18631,18678,18766,18835,18890,18960,19034,19084,19622,19678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e535ebbc8274cd26275a634ce7617c3d\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "2,3,4,10,11,20,21,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,96,97,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,129,137,138,139,140,141,142,143,277,308,309,313,314,318,332,333,373,379,389,424,454,487", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,661,726,1463,1532,2011,2081,2149,2221,2291,2352,2426,2499,2560,2621,2683,2747,2809,2870,2938,3038,3098,3164,3237,3306,3363,3415,3477,3549,3625,6120,6155,6417,6472,6535,6590,6648,6704,6762,6823,6886,6943,6994,7052,7102,7163,7220,7286,7320,7355,7898,8411,8478,8550,8619,8688,8762,8834,19217,20969,21086,21287,21397,21598,22442,22514,23772,23975,24276,26082,27082,27764", "endLines": "2,3,4,10,11,20,21,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,96,97,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,129,137,138,139,140,141,142,143,277,308,312,313,317,318,332,333,378,388,423,444,486,492", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,277,365,721,787,1527,1590,2076,2144,2216,2286,2347,2421,2494,2555,2616,2678,2742,2804,2865,2933,3033,3093,3159,3232,3301,3358,3410,3472,3544,3620,3685,6150,6185,6467,6530,6585,6643,6699,6757,6818,6881,6938,6989,7047,7097,7158,7215,7281,7315,7350,7385,7963,8473,8545,8614,8683,8757,8829,8917,19283,21081,21282,21392,21593,21722,22509,22576,23970,24271,26077,26758,27759,27926"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b44500860a60172f953a4f023e793d5e\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "281,282", "startColumns": "4,4", "startOffsets": "19461,19517", "endColumns": "55,54", "endOffsets": "19512,19567"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\edf9c8020cd50a188330c26fb3418bd5\\transformed\\coil-base-2.5.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "5674", "endColumns": "49", "endOffsets": "5719"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d10c4aa197655201a0072f140ac05a09\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "92", "startColumns": "4", "startOffsets": "5894", "endColumns": "65", "endOffsets": "5955"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\34c85abedd6223b113a6accd9f80526a\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "121", "startColumns": "4", "startOffsets": "7433", "endColumns": "42", "endOffsets": "7471"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a24524cd577336c01efc449f32e7672\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "341,357,363,564,580", "startColumns": "4,4,4,4,4", "startOffsets": "22885,23310,23488,30338,30749", "endLines": "356,362,372,579,583", "endColumns": "24,24,24,24,24", "endOffsets": "23305,23483,23767,30744,30871"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9f3b0e192fae8d81415bbd16b5199dc2\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "6,7,8,9,28,29,166,175,176,177", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "412,470,536,599,1868,1939,11466,12108,12175,12254", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "465,531,594,656,1934,2006,11529,12170,12249,12318"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bafe67932525e9d41a7253ce026a7fa1\\transformed\\core-common-2.0.3\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endLines": "11", "endColumns": "8", "endOffsets": "571"}, "to": {"startLines": "323", "startColumns": "4", "startOffsets": "21950", "endLines": "330", "endColumns": "8", "endOffsets": "22355"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e9a72f63514fa4408a66e1f6acc37e53\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "124", "startColumns": "4", "startOffsets": "7590", "endColumns": "49", "endOffsets": "7635"}}, {"source": "C:\\Users\\<USER>\\Desktop\\trust\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "5,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "370,1595,1642,1689,1736,1781,1826", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "407,1637,1684,1731,1776,1821,1863"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fbc79d900062b4e4226447ae7937fc74\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "120", "startColumns": "4", "startOffsets": "7390", "endColumns": "42", "endOffsets": "7428"}}, {"source": "C:\\Users\\<USER>\\Desktop\\trust\\app\\build\\generated\\res\\processReleaseGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,199,316,398,502,611,731,824", "endColumns": "143,116,81,103,108,119,92,69", "endOffsets": "194,311,393,497,606,726,819,889"}, "to": {"startLines": "172,184,185,186,187,188,189,259", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "11867,12716,12833,12915,13019,13128,13248,18209", "endColumns": "143,116,81,103,108,119,92,69", "endOffsets": "12006,12828,12910,13014,13123,13243,13336,18274"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\27e7e2f5ae47ff80d1406eee848a3aa7\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,89,90,93,94,126,146,147,170,171,174,191,192,255,257,260,261,265,274,275,276,278,279,280,285,301,304", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3690,3749,3808,3868,3928,3988,4048,4108,4168,4228,4288,4348,4408,4467,4527,4587,4647,4707,4767,4827,4887,4947,5007,5067,5126,5186,5246,5305,5364,5423,5482,5541,5600,5724,5782,5960,6011,7704,9014,9079,11700,11766,12050,13388,13440,17980,18097,18279,18329,18551,19089,19135,19177,19288,19335,19371,19683,20663,20774", "endLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,89,90,93,94,126,146,147,170,171,174,191,192,255,257,260,261,265,274,275,276,278,279,280,287,303,307", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "3744,3803,3863,3923,3983,4043,4103,4163,4223,4283,4343,4403,4462,4522,4582,4642,4702,4762,4822,4882,4942,5002,5062,5121,5181,5241,5300,5359,5418,5477,5536,5595,5669,5777,5832,6006,6061,7752,9074,9128,11761,11862,12103,13435,13495,18037,18146,18324,18378,18592,19130,19172,19212,19330,19366,19456,19790,20769,20964"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9f69366cbf0646f5d6bc24c653e3f29b\\transformed\\navigation-runtime-2.8.5\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "98,334,534,537", "startColumns": "4,4,4,4", "startOffsets": "6190,22581,29509,29624", "endLines": "98,340,536,539", "endColumns": "52,24,24,24", "endOffsets": "6238,22880,29619,29734"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\63a46a854a76ede8c8143222479f06da\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "100,122", "startColumns": "4,4", "startOffsets": "6310,7476", "endColumns": "41,59", "endOffsets": "6347,7531"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\63ccc495eef38375f13c6ff523cfbb5a\\transformed\\play-services-basement-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "127,156", "startColumns": "4,4", "startOffsets": "7757,10173", "endColumns": "67,166", "endOffsets": "7820,10335"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a4406fb22280fb302c67f992ef8133c\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "128,194,195,196,197,198,199,200,201,202,203,206,207,208,209,210,211,212,213,214,215,216,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,288,298", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7825,13539,13627,13713,13794,13878,13947,14012,14095,14201,14287,14407,14461,14530,14591,14660,14749,14844,14918,15015,15108,15206,15355,15446,15534,15630,15728,15792,15860,15947,16041,16108,16180,16252,16353,16462,16538,16607,16655,16721,16785,16859,16916,16973,17045,17095,17149,17220,17291,17361,17430,17488,17564,17635,17709,17795,17845,17915,19795,20510", "endLines": "128,194,195,196,197,198,199,200,201,202,205,206,207,208,209,210,211,212,213,214,215,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,297,300", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "7893,13622,13708,13789,13873,13942,14007,14090,14196,14282,14402,14456,14525,14586,14655,14744,14839,14913,15010,15103,15201,15350,15441,15529,15625,15723,15787,15855,15942,16036,16103,16175,16247,16348,16457,16533,16602,16650,16716,16780,16854,16911,16968,17040,17090,17144,17215,17286,17356,17425,17483,17559,17630,17704,17790,17840,17910,17975,20505,20658"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9cef95d772ca94c574c1d7981123fcc6\\transformed\\play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "12,13,14,15,16,17,18,19,148,149,150,151,152,153,154,155,157,158,159,160,161,162,163,164,165,493,545", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "792,882,962,1052,1142,1222,1303,1383,9133,9238,9419,9544,9651,9831,9954,10070,10340,10528,10633,10814,10939,11114,11262,11325,11387,27931,29921", "endLines": "12,13,14,15,16,17,18,19,148,149,150,151,152,153,154,155,157,158,159,160,161,162,163,164,165,505,563", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "877,957,1047,1137,1217,1298,1378,1458,9233,9414,9539,9646,9826,9949,10065,10168,10523,10628,10809,10934,11109,11257,11320,11382,11461,28241,30333"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\35b39c5d4586767030702f69abdc3127\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "123", "startColumns": "4", "startOffsets": "7536", "endColumns": "53", "endOffsets": "7585"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9218a466516f7e267d053272ca47869c\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "91,101,125,445,450", "startColumns": "4,4,4,4,4", "startOffsets": "5837,6352,7640,26763,26933", "endLines": "91,101,125,449,453", "endColumns": "56,64,63,24,24", "endOffsets": "5889,6412,7699,26928,27077"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\738aced92cf0d3e841a31cacc78effce\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "95,99", "startColumns": "4,4", "startOffsets": "6066,6243", "endColumns": "53,66", "endOffsets": "6115,6305"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\518565318f569a8f40477eeed556b992\\transformed\\credentials-play-services-auth-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "273"}, "to": {"startLines": "319", "startColumns": "4", "startOffsets": "21727", "endLines": "322", "endColumns": "12", "endOffsets": "21945"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\34a6171889a3f0a2e7cbf13a2f1c0e80\\transformed\\credentials-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,83", "endOffsets": "132,216"}, "to": {"startLines": "130,131", "startColumns": "4,4", "startOffsets": "7968,8050", "endColumns": "81,83", "endOffsets": "8045,8129"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3494e4d616dcc70de5ac033bfdba3623\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "132", "startColumns": "4", "startOffsets": "8134", "endColumns": "82", "endOffsets": "8212"}}]}]}