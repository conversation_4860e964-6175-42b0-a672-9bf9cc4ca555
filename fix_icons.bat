@echo off
echo Fixing icon references...

REM Replace all problematic icons with available ones
powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\components\RoomCard.kt') -replace 'Icons.Default.Forum', 'Icons.Default.Chat' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\components\RoomCard.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\components\RoomCard.kt') -replace 'Icons.Default.Group', 'Icons.Default.People' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\components\RoomCard.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\HomeScreen.kt') -replace 'Icons.Default.Games', 'Icons.Default.Star' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\HomeScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\HomeScreen.kt') -replace 'Icons.Default.Chat', 'Icons.Default.Message' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\HomeScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\HomeScreen.kt') -replace 'Icons.Default.Forum', 'Icons.Default.Chat' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\HomeScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\LuckyWheelScreen.kt') -replace 'Icons.Default.LocalActivity', 'Icons.Default.Star' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\LuckyWheelScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\LuckyWheelScreen.kt') -replace 'Icons.Default.Games', 'Icons.Default.Star' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\LuckyWheelScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\NotificationsScreen.kt') -replace 'Icons.Default.Chat', 'Icons.Default.Message' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\NotificationsScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\NotificationsScreen.kt') -replace 'Icons.Default.AlternateEmail', 'Icons.Default.Email' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\NotificationsScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\NotificationsScreen.kt') -replace 'Icons.Default.Redeem', 'Icons.Default.Star' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\NotificationsScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\PrivateChatScreen.kt') -replace 'Icons.Default.Videocam', 'Icons.Default.VideoCall' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\PrivateChatScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\PrivateChatScreen.kt') -replace 'Icons.Default.AttachFile', 'Icons.Default.Attachment' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\PrivateChatScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\PrivateChatsScreen.kt') -replace 'Icons.Default.Chat', 'Icons.Default.Message' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\PrivateChatsScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt') -replace 'Icons.Default.Translate', 'Icons.Default.Language' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt') -replace 'Icons.Default.Shield', 'Icons.Default.Security' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt') -replace 'Icons.Default.ColorLens', 'Icons.Default.Palette' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt') -replace 'Icons.Default.Folder', 'Icons.Default.Storage' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt') -replace 'Icons.Default.Help', 'Icons.Default.HelpOutline' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt') -replace 'Icons.Default.Article', 'Icons.Default.Description' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\AdminScreens.kt') -replace 'Icons.Default.DeleteSweep', 'Icons.Default.Delete' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\AdminScreens.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\AdminScreens.kt') -replace 'Icons.Default.Block', 'Icons.Default.Block' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\AdminScreens.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\ChatScreen.kt') -replace 'Icons.Default.Attachment', 'Icons.Default.AttachFile' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\ChatScreen.kt'"

echo Icons fixed!
