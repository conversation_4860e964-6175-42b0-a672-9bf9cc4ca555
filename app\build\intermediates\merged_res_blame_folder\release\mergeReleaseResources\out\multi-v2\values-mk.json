{"logs": [{"outputFile": "com.trustmarket.trust.app-mergeReleaseResources-66:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e535ebbc8274cd26275a634ce7617c3d\\transformed\\core-1.16.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "4,5,6,7,8,9,10,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "341,439,541,638,736,841,944,11380", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "434,536,633,731,836,939,1055,11476"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9cef95d772ca94c574c1d7981123fcc6\\transformed\\play-services-base-18.5.0\\res\\values-mk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,453,582,688,829,958,1074,1180,1333,1436,1598,1727,1876,2031,2096,2156", "endColumns": "102,156,128,105,140,128,115,105,152,102,161,128,148,154,64,59,74", "endOffsets": "295,452,581,687,828,957,1073,1179,1332,1435,1597,1726,1875,2030,2095,2155,2230"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1254,1361,1522,1655,1765,1910,2043,2163,2410,2567,2674,2840,2973,3126,3285,3354,3418", "endColumns": "106,160,132,109,144,132,119,109,156,106,165,132,152,158,68,63,78", "endOffsets": "1356,1517,1650,1760,1905,2038,2158,2268,2562,2669,2835,2968,3121,3280,3349,3413,3492"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\63ccc495eef38375f13c6ff523cfbb5a\\transformed\\play-services-basement-18.5.0\\res\\values-mk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "132", "endOffsets": "327"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2273", "endColumns": "136", "endOffsets": "2405"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a4406fb22280fb302c67f992ef8133c\\transformed\\material3-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,292,408,526,623,718,830,963,1084,1232,1317,1416,1510,1606,1721,1845,1949,2094,2238,2380,2554,2685,2806,2933,3058,3153,3251,3377,3512,3612,3714,3827,3968,4117,4233,4335,4412,4506,4601,4720,4812,4898,5012,5095,5178,5278,5380,5477,5574,5662,5769,5869,5971,6104,6187,6298", "endColumns": "118,117,115,117,96,94,111,132,120,147,84,98,93,95,114,123,103,144,143,141,173,130,120,126,124,94,97,125,134,99,101,112,140,148,115,101,76,93,94,118,91,85,113,82,82,99,101,96,96,87,106,99,101,132,82,110,102", "endOffsets": "169,287,403,521,618,713,825,958,1079,1227,1312,1411,1505,1601,1716,1840,1944,2089,2233,2375,2549,2680,2801,2928,3053,3148,3246,3372,3507,3607,3709,3822,3963,4112,4228,4330,4407,4501,4596,4715,4807,4893,5007,5090,5173,5273,5375,5472,5569,5657,5764,5864,5966,6099,6182,6293,6396"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4382,4501,4619,4735,4853,4950,5045,5157,5290,5411,5559,5644,5743,5837,5933,6048,6172,6276,6421,6565,6707,6881,7012,7133,7260,7385,7480,7578,7704,7839,7939,8041,8154,8295,8444,8560,8662,8739,8833,8928,9047,9139,9225,9339,9422,9505,9605,9707,9804,9901,9989,10096,10196,10298,10431,10514,10625", "endColumns": "118,117,115,117,96,94,111,132,120,147,84,98,93,95,114,123,103,144,143,141,173,130,120,126,124,94,97,125,134,99,101,112,140,148,115,101,76,93,94,118,91,85,113,82,82,99,101,96,96,87,106,99,101,132,82,110,102", "endOffsets": "4496,4614,4730,4848,4945,5040,5152,5285,5406,5554,5639,5738,5832,5928,6043,6167,6271,6416,6560,6702,6876,7007,7128,7255,7380,7475,7573,7699,7834,7934,8036,8149,8290,8439,8555,8657,8734,8828,8923,9042,9134,9220,9334,9417,9500,9600,9702,9799,9896,9984,10091,10191,10293,10426,10509,10620,10723"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\27e7e2f5ae47ff80d1406eee848a3aa7\\transformed\\ui-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,299,395,498,583,660,750,842,926,1010,1099,1171,1248,1326,1402,1483,1554", "endColumns": "103,89,95,102,84,76,89,91,83,83,88,71,76,77,75,80,70,120", "endOffsets": "204,294,390,493,578,655,745,837,921,1005,1094,1166,1243,1321,1397,1478,1549,1670"}, "to": {"startLines": "11,12,32,33,34,38,39,97,98,99,100,101,102,103,104,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1060,1164,3610,3706,3809,4215,4292,10728,10820,10904,10988,11077,11149,11226,11304,11481,11562,11633", "endColumns": "103,89,95,102,84,76,89,91,83,83,88,71,76,77,75,80,70,120", "endOffsets": "1159,1249,3701,3804,3889,4287,4377,10815,10899,10983,11072,11144,11221,11299,11375,11557,11628,11749"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9f3b0e192fae8d81415bbd16b5199dc2\\transformed\\browser-1.4.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,168,273,388", "endColumns": "112,104,114,100", "endOffsets": "163,268,383,484"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3497,3894,3999,4114", "endColumns": "112,104,114,100", "endOffsets": "3605,3994,4109,4210"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\34a6171889a3f0a2e7cbf13a2f1c0e80\\transformed\\credentials-1.2.0-rc01\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,124", "endOffsets": "161,286"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,216", "endColumns": "110,124", "endOffsets": "211,336"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b44500860a60172f953a4f023e793d5e\\transformed\\foundation-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,149", "endColumns": "93,95", "endOffsets": "144,240"}, "to": {"startLines": "109,110", "startColumns": "4,4", "startOffsets": "11754,11848", "endColumns": "93,95", "endOffsets": "11843,11939"}}]}]}