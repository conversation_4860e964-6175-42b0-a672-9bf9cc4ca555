1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.trustmarket.trust"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Internet permission for Firebase and WebView -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.WAKE_LOCK" />
14-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:25:5-68
14-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:25:22-65
15    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
15-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
15-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
16    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
16-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2269f6a490db479487e1f60b8c759e15\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
16-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2269f6a490db479487e1f60b8c759e15\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:25:22-76
17    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
17-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2269f6a490db479487e1f60b8c759e15\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
17-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2269f6a490db479487e1f60b8c759e15\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
18    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
18-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2269f6a490db479487e1f60b8c759e15\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
18-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2269f6a490db479487e1f60b8c759e15\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
19    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
19-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\290fe9217f4b4b879ffe8d2a89d2ccc6\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
19-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\290fe9217f4b4b879ffe8d2a89d2ccc6\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
20
21    <permission
21-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e535ebbc8274cd26275a634ce7617c3d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
22        android:name="com.trustmarket.trust.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
22-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e535ebbc8274cd26275a634ce7617c3d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
23        android:protectionLevel="signature" />
23-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e535ebbc8274cd26275a634ce7617c3d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
24
25    <uses-permission android:name="com.trustmarket.trust.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
25-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e535ebbc8274cd26275a634ce7617c3d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
25-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e535ebbc8274cd26275a634ce7617c3d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
26
27    <application
27-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:9:5-30:19
28        android:allowBackup="true"
28-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:10:9-35
29        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
29-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e535ebbc8274cd26275a634ce7617c3d\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
30        android:dataExtractionRules="@xml/data_extraction_rules"
30-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:11:9-65
31        android:extractNativeLibs="false"
32        android:fullBackupContent="@xml/backup_rules"
32-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:12:9-54
33        android:icon="@mipmap/ic_launcher"
33-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:13:9-43
34        android:label="@string/app_name"
34-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:14:9-41
35        android:roundIcon="@mipmap/ic_launcher_round"
35-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:15:9-54
36        android:supportsRtl="true"
36-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:16:9-35
37        android:theme="@style/Theme.Trust" >
37-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:17:9-43
38        <activity
38-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:19:9-29:20
39            android:name="com.trustmarket.trust.MainActivity"
39-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:20:13-41
40            android:exported="true"
40-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:21:13-36
41            android:label="@string/app_name"
41-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:22:13-45
42            android:theme="@style/Theme.Trust" >
42-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:23:13-47
43            <intent-filter>
43-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:24:13-28:29
44                <action android:name="android.intent.action.MAIN" />
44-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:25:17-69
44-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:25:25-66
45
46                <category android:name="android.intent.category.LAUNCHER" />
46-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:27:17-77
46-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:27:27-74
47            </intent-filter>
48        </activity>
49        <activity
49-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
50            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
50-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
51            android:excludeFromRecents="true"
51-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
52            android:exported="true"
52-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
53            android:launchMode="singleTask"
53-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
54            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
54-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
55            <intent-filter>
55-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
56                <action android:name="android.intent.action.VIEW" />
56-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
56-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
57
58                <category android:name="android.intent.category.DEFAULT" />
58-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
58-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
59                <category android:name="android.intent.category.BROWSABLE" />
59-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
59-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
60
61                <data
61-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
62                    android:host="firebase.auth"
62-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
63                    android:path="/"
63-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
64                    android:scheme="genericidp" />
64-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
65            </intent-filter>
66        </activity>
67        <activity
67-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
68            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
68-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
69            android:excludeFromRecents="true"
69-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
70            android:exported="true"
70-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
71            android:launchMode="singleTask"
71-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
72            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
72-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
73            <intent-filter>
73-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
74                <action android:name="android.intent.action.VIEW" />
74-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
74-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
75
76                <category android:name="android.intent.category.DEFAULT" />
76-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
76-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
77                <category android:name="android.intent.category.BROWSABLE" />
77-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
77-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
78
79                <data
79-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
80                    android:host="firebase.auth"
80-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
81                    android:path="/"
81-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
82                    android:scheme="recaptcha" />
82-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
83            </intent-filter>
84        </activity>
85
86        <service
86-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
87            android:name="com.google.firebase.components.ComponentDiscoveryService"
87-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:67:13-84
88            android:directBootAware="true"
88-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
89            android:exported="false" >
89-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
90            <meta-data
90-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
91                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
91-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
92                android:value="com.google.firebase.components.ComponentRegistrar" />
92-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
93            <meta-data
93-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afec609340cf35cbdd99288228d31f83\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
94                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
94-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afec609340cf35cbdd99288228d31f83\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
95                android:value="com.google.firebase.components.ComponentRegistrar" />
95-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afec609340cf35cbdd99288228d31f83\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
96            <meta-data
96-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afec609340cf35cbdd99288228d31f83\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
97                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
97-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afec609340cf35cbdd99288228d31f83\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
98                android:value="com.google.firebase.components.ComponentRegistrar" />
98-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afec609340cf35cbdd99288228d31f83\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
99            <meta-data
99-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2269f6a490db479487e1f60b8c759e15\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
100                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
100-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2269f6a490db479487e1f60b8c759e15\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
101                android:value="com.google.firebase.components.ComponentRegistrar" />
101-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2269f6a490db479487e1f60b8c759e15\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
102            <meta-data
102-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a255781ae5900fa88d835456e104a7b8\transformed\firebase-storage-21.0.2\AndroidManifest.xml:30:13-32:85
103                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
103-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a255781ae5900fa88d835456e104a7b8\transformed\firebase-storage-21.0.2\AndroidManifest.xml:31:17-118
104                android:value="com.google.firebase.components.ComponentRegistrar" />
104-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a255781ae5900fa88d835456e104a7b8\transformed\firebase-storage-21.0.2\AndroidManifest.xml:32:17-82
105            <meta-data
105-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a255781ae5900fa88d835456e104a7b8\transformed\firebase-storage-21.0.2\AndroidManifest.xml:33:13-35:85
106                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
106-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a255781ae5900fa88d835456e104a7b8\transformed\firebase-storage-21.0.2\AndroidManifest.xml:34:17-107
107                android:value="com.google.firebase.components.ComponentRegistrar" />
107-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a255781ae5900fa88d835456e104a7b8\transformed\firebase-storage-21.0.2\AndroidManifest.xml:35:17-82
108            <meta-data
108-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
109                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
109-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
110                android:value="com.google.firebase.components.ComponentRegistrar" />
110-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
111            <meta-data
111-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
112                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
112-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
113                android:value="com.google.firebase.components.ComponentRegistrar" />
113-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
114            <meta-data
114-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
115                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
115-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
116                android:value="com.google.firebase.components.ComponentRegistrar" />
116-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
117            <meta-data
117-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
118                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
118-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
119                android:value="com.google.firebase.components.ComponentRegistrar" />
119-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
120            <meta-data
120-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8a49351139c0acd4d23330a6344893\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
121                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
121-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8a49351139c0acd4d23330a6344893\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
122                android:value="com.google.firebase.components.ComponentRegistrar" />
122-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8a49351139c0acd4d23330a6344893\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
123            <meta-data
123-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
124                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
124-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
125                android:value="com.google.firebase.components.ComponentRegistrar" />
125-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
126        </service>
127
128        <receiver
128-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
129            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
129-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
130            android:enabled="true"
130-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
131            android:exported="false" >
131-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
132        </receiver>
133
134        <service
134-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
135            android:name="com.google.android.gms.measurement.AppMeasurementService"
135-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
136            android:enabled="true"
136-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
137            android:exported="false" />
137-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
138        <service
138-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
139            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
139-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
140            android:enabled="true"
140-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
141            android:exported="false"
141-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
142            android:permission="android.permission.BIND_JOB_SERVICE" />
142-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
143        <service
143-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
144            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
144-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
145            android:enabled="true"
145-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
146            android:exported="false" >
146-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
147            <meta-data
147-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
148                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
148-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
149                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
149-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
150        </service>
151
152        <activity
152-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
153            android:name="androidx.credentials.playservices.HiddenActivity"
153-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
154            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
154-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
155            android:enabled="true"
155-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
156            android:exported="false"
156-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
157            android:fitsSystemWindows="true"
157-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
158            android:theme="@style/Theme.Hidden" >
158-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
159        </activity>
160        <activity
160-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60a93836d30073ec518084db27eb686c\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
161            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
161-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60a93836d30073ec518084db27eb686c\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
162            android:excludeFromRecents="true"
162-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60a93836d30073ec518084db27eb686c\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
163            android:exported="false"
163-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60a93836d30073ec518084db27eb686c\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
164            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
164-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60a93836d30073ec518084db27eb686c\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
165        <!--
166            Service handling Google Sign-In user revocation. For apps that do not integrate with
167            Google Sign-In, this service will never be started.
168        -->
169        <service
169-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60a93836d30073ec518084db27eb686c\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
170            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
170-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60a93836d30073ec518084db27eb686c\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
171            android:exported="true"
171-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60a93836d30073ec518084db27eb686c\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
172            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
172-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60a93836d30073ec518084db27eb686c\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
173            android:visibleToInstantApps="true" />
173-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60a93836d30073ec518084db27eb686c\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
174
175        <provider
175-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
176            android:name="com.google.firebase.provider.FirebaseInitProvider"
176-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
177            android:authorities="com.trustmarket.trust.firebaseinitprovider"
177-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
178            android:directBootAware="true"
178-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
179            android:exported="false"
179-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
180            android:initOrder="100" />
180-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
181
182        <activity
182-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
183            android:name="com.google.android.gms.common.api.GoogleApiActivity"
183-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
184            android:exported="false"
184-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
185            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
185-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
186
187        <provider
187-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
188            android:name="androidx.startup.InitializationProvider"
188-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
189            android:authorities="com.trustmarket.trust.androidx-startup"
189-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
190            android:exported="false" >
190-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
191            <meta-data
191-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
192                android:name="androidx.emoji2.text.EmojiCompatInitializer"
192-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
193                android:value="androidx.startup" />
193-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
194            <meta-data
194-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b24052f1db77d6bbe4309c4f2fc3cb7\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
195                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
195-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b24052f1db77d6bbe4309c4f2fc3cb7\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
196                android:value="androidx.startup" />
196-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b24052f1db77d6bbe4309c4f2fc3cb7\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
197            <meta-data
197-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
198                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
198-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
199                android:value="androidx.startup" />
199-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
200        </provider>
201
202        <uses-library
202-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aec5efcf300c1ea9f0193c5ce10a1517\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
203            android:name="android.ext.adservices"
203-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aec5efcf300c1ea9f0193c5ce10a1517\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
204            android:required="false" />
204-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aec5efcf300c1ea9f0193c5ce10a1517\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
205
206        <meta-data
206-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63ccc495eef38375f13c6ff523cfbb5a\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
207            android:name="com.google.android.gms.version"
207-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63ccc495eef38375f13c6ff523cfbb5a\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
208            android:value="@integer/google_play_services_version" />
208-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63ccc495eef38375f13c6ff523cfbb5a\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
209
210        <receiver
210-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
211            android:name="androidx.profileinstaller.ProfileInstallReceiver"
211-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
212            android:directBootAware="false"
212-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
213            android:enabled="true"
213-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
214            android:exported="true"
214-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
215            android:permission="android.permission.DUMP" >
215-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
216            <intent-filter>
216-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
217                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
217-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
217-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
218            </intent-filter>
219            <intent-filter>
219-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
220                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
220-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
220-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
221            </intent-filter>
222            <intent-filter>
222-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
223                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
223-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
223-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
224            </intent-filter>
225            <intent-filter>
225-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
226                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
226-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
226-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
227            </intent-filter>
228        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
229        <activity
229-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
230            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
230-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
231            android:exported="false"
231-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
232            android:stateNotNeeded="true"
232-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
233            android:theme="@style/Theme.PlayCore.Transparent" />
233-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
234    </application>
235
236</manifest>
