package com.trustmarket.trust.ui.screens

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import com.trustmarket.trust.data.models.User

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    onBackClick: () -> Unit
) {
    var showLanguageDialog by remember { mutableStateOf(false) }
    var showSupportDialog by remember { mutableStateOf(false) }
    var showTermsDialog by remember { mutableStateOf(false) }
    var showLogoutDialog by remember { mutableStateOf(false) }
    
    // Mock current user - replace with real data
    val currentUser = remember {
        User(
            uid = "currentUser",
            name = "المستخدم الحالي",
            email = "<EMAIL>",
            avatar = "",
            isOnline = true
        )
    }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Text(
                    text = "الإعدادات",
                    fontWeight = FontWeight.Bold
                )
            },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "العودة"
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = MaterialTheme.colorScheme.primary,
                titleContentColor = MaterialTheme.colorScheme.onPrimary,
                navigationIconContentColor = MaterialTheme.colorScheme.onPrimary
            )
        )
        
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Profile Section
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        AsyncImage(
                            model = currentUser.avatar.ifEmpty { "https://via.placeholder.com/60" },
                            contentDescription = "صورة الملف الشخصي",
                            modifier = Modifier
                                .size(60.dp)
                                .clip(CircleShape),
                            contentScale = ContentScale.Crop
                        )
                        
                        Spacer(modifier = Modifier.width(16.dp))
                        
                        Column(
                            modifier = Modifier.weight(1f)
                        ) {
                            Text(
                                text = currentUser.name,
                                fontSize = 18.sp,
                                fontWeight = FontWeight.Bold
                            )
                            
                            Text(
                                text = currentUser.email,
                                fontSize = 14.sp,
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                            )
                        }
                        
                        IconButton(onClick = { /* Edit profile */ }) {
                            Icon(
                                imageVector = Icons.Default.Edit,
                                contentDescription = "تعديل الملف الشخصي"
                            )
                        }
                    }
                }
            }
            
            // General Settings
            item {
                Text(
                    text = "عام",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }
            
            item {
                SettingsCard(
                    icon = Icons.Default.Settings,
                    title = "اللغة",
                    subtitle = "العربية",
                    onClick = { showLanguageDialog = true }
                )
            }
            
            item {
                SettingsCard(
                    icon = Icons.Default.Notifications,
                    title = "الإشعارات",
                    subtitle = "إدارة إعدادات الإشعارات",
                    onClick = { /* Navigate to notifications settings */ }
                )
            }
            
            item {
                SettingsCard(
                    icon = Icons.Default.Lock,
                    title = "الخصوصية والأمان",
                    subtitle = "إعدادات الخصوصية",
                    onClick = { /* Navigate to privacy settings */ }
                )
            }
            
            // App Settings
            item {
                Text(
                    text = "التطبيق",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }
            
            item {
                SettingsCard(
                    icon = Icons.Default.Settings,
                    title = "المظهر",
                    subtitle = "فاتح، داكن، تلقائي",
                    onClick = { /* Theme settings */ }
                )
            }
            
            item {
                SettingsCard(
                    icon = Icons.Default.Settings,
                    title = "التخزين",
                    subtitle = "إدارة ملفات التطبيق",
                    onClick = { /* Storage settings */ }
                )
            }
            
            // Support & Info
            item {
                Text(
                    text = "الدعم والمعلومات",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }
            
            item {
                SettingsCard(
                    icon = Icons.Default.Help,
                    title = "المساعدة والدعم",
                    subtitle = "الحصول على المساعدة",
                    onClick = { showSupportDialog = true }
                )
            }
            
            item {
                SettingsCard(
                    icon = Icons.Default.Info,
                    title = "حول التطبيق",
                    subtitle = "الإصدار 1.0.0",
                    onClick = { /* About app */ }
                )
            }
            
            item {
                SettingsCard(
                    icon = Icons.Default.Description,
                    title = "الشروط والأحكام",
                    subtitle = "اقرأ الشروط والأحكام",
                    onClick = { showTermsDialog = true }
                )
            }
            
            // Logout
            item {
                Spacer(modifier = Modifier.height(16.dp))
                
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { showLogoutDialog = true },
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.ExitToApp,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onErrorContainer
                        )
                        
                        Spacer(modifier = Modifier.width(16.dp))
                        
                        Text(
                            text = "تسجيل الخروج",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                    }
                }
            }
        }
    }
    
    // Dialogs
    if (showLanguageDialog) {
        LanguageDialog(
            onDismiss = { showLanguageDialog = false },
            onLanguageSelected = { language ->
                // Handle language change
                showLanguageDialog = false
            }
        )
    }
    
    if (showSupportDialog) {
        SupportDialog(
            onDismiss = { showSupportDialog = false }
        )
    }
    
    if (showTermsDialog) {
        TermsDialog(
            onDismiss = { showTermsDialog = false }
        )
    }
    
    if (showLogoutDialog) {
        LogoutDialog(
            onDismiss = { showLogoutDialog = false },
            onConfirm = {
                // Handle logout
                showLogoutDialog = false
                onBackClick()
            }
        )
    }
}

@Composable
fun SettingsCard(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    subtitle: String,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                
                Text(
                    text = subtitle,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
            
            Icon(
                imageVector = Icons.Default.KeyboardArrowRight,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
            )
        }
    }
}

@Composable
fun LanguageDialog(
    onDismiss: () -> Unit,
    onLanguageSelected: (String) -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("اختر اللغة") },
        text = {
            Column {
                val languages = listOf("العربية", "English", "Français")
                languages.forEach { language ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onLanguageSelected(language) }
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = language == "العربية",
                            onClick = { onLanguageSelected(language) }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(language)
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("إلغاء")
            }
        }
    )
}

@Composable
fun SupportDialog(onDismiss: () -> Unit) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("المساعدة والدعم") },
        text = {
            Text("للحصول على المساعدة، يرجى التواصل معنا عبر:\n\nالبريد الإلكتروني: <EMAIL>\nالهاتف: +966 50 123 4567")
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("حسناً")
            }
        }
    )
}

@Composable
fun TermsDialog(onDismiss: () -> Unit) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("الشروط والأحكام") },
        text = {
            Text("هذه هي الشروط والأحكام الخاصة بتطبيق Trust Market...\n\n1. استخدام التطبيق\n2. الخصوصية\n3. المسؤوليات")
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("موافق")
            }
        }
    )
}

@Composable
fun LogoutDialog(
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("تسجيل الخروج") },
        text = { Text("هل أنت متأكد من أنك تريد تسجيل الخروج؟") },
        confirmButton = {
            TextButton(onClick = onConfirm) {
                Text("تسجيل الخروج")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("إلغاء")
            }
        }
    )
}
