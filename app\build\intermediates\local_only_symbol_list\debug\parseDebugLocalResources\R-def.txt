R_DEF: Internal format may change without notice
local
color black
color purple_200
color purple_500
color purple_700
color teal_200
color teal_700
color white
drawable ic_launcher_background
drawable ic_launcher_foreground
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
string attach_file
string audio_message
string back
string cancel
string chat_rooms
string create
string create_room
string create_room_hint
string default_web_client_id
string delete
string feature_easy_ui
string feature_file_sharing
string feature_group_chat
string feature_notifications
string feature_private_chat
string features
string firebase_database_url
string gcm_defaultSenderId
string google_api_key
string google_app_id
string google_crash_reporting_api_key
string google_storage_bucket
string image_message
string like
string no_rooms
string private_chats
string project_id
string room_description
string room_info
string room_name
string send
string settings
string sign_in_description
string sign_in_subtitle
string sign_in_title
string sign_in_welcome
string sign_in_with_google
string sign_out
string type_message
string users_online
style Theme.Trust
xml backup_rules
xml data_extraction_rules
