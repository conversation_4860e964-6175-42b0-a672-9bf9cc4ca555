package com.trustmarket.trust.ui.screens

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.center
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.rotate
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.trustmarket.trust.data.models.LuckyWheelReward
import com.trustmarket.trust.data.models.UserTickets
import com.trustmarket.trust.data.models.WheelSpin
import kotlin.math.*
import kotlin.random.Random

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LuckyWheelScreen(
    onBackClick: () -> Unit
) {
    var userTickets by remember { mutableStateOf(UserTickets(tickets = 25)) }
    var isSpinning by remember { mutableStateOf(false) }
    var showRewardDialog by remember { mutableStateOf(false) }
    var currentReward by remember { mutableStateOf<LuckyWheelReward?>(null) }
    var showHistoryDialog by remember { mutableStateOf(false) }
    
    val rewards = remember {
        listOf(
            LuckyWheelReward("1", "5 تذاكر", "احصل على 5 تذاكر إضافية", "tickets", 5, "#FF6B6B", 0.3),
            LuckyWheelReward("2", "10 تذاكر", "احصل على 10 تذاكر إضافية", "tickets", 10, "#4ECDC4", 0.25),
            LuckyWheelReward("3", "20 تذاكر", "احصل على 20 تذكرة إضافية", "tickets", 20, "#45B7D1", 0.2),
            LuckyWheelReward("4", "شارة ذهبية", "شارة ذهبية مميزة", "badge", 1, "#FFD93D", 0.1),
            LuckyWheelReward("5", "50 تذكرة", "جائزة كبيرة!", "tickets", 50, "#6BCF7F", 0.08),
            LuckyWheelReward("6", "محاولة أخرى", "حاول مرة أخرى مجاناً", "special", 0, "#A8E6CF", 0.05),
            LuckyWheelReward("7", "شارة فضية", "شارة فضية جميلة", "badge", 1, "#C0C0C0", 0.15),
            LuckyWheelReward("8", "100 تذكرة", "الجائزة الكبرى!", "tickets", 100, "#FF8A80", 0.02)
        )
    }
    
    val spinHistory = remember {
        listOf(
            WheelSpin("1", "user", "أنت", "1", "5 تذاكر", 5, System.currentTimeMillis() - 3600000),
            WheelSpin("2", "user", "أنت", "3", "20 تذكرة", 20, System.currentTimeMillis() - 7200000)
        )
    }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Text(
                    text = "عجلة الحظ",
                    fontWeight = FontWeight.Bold
                )
            },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "العودة"
                    )
                }
            },
            actions = {
                IconButton(onClick = { showHistoryDialog = true }) {
                    Icon(
                        imageVector = Icons.Default.List,
                        contentDescription = "السجل"
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = MaterialTheme.colorScheme.primary,
                titleContentColor = MaterialTheme.colorScheme.onPrimary,
                navigationIconContentColor = MaterialTheme.colorScheme.onPrimary,
                actionIconContentColor = MaterialTheme.colorScheme.onPrimary
            )
        )
        
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Tickets Card
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(20.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column {
                            Text(
                                text = "تذاكرك",
                                fontSize = 16.sp,
                                color = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                            Text(
                                text = "${userTickets.tickets}",
                                fontSize = 32.sp,
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                        }
                        
                        Icon(
                            imageVector = Icons.Default.Star,
                            contentDescription = null,
                            modifier = Modifier.size(48.dp),
                            tint = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }
                }
            }
            
            // Lucky Wheel
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(20.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "أدر العجلة واربح جوائز رائعة!",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.SemiBold,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.padding(bottom = 20.dp)
                        )
                        
                        // Wheel Component
                        LuckyWheelComponent(
                            rewards = rewards,
                            isSpinning = isSpinning,
                            onSpinComplete = { reward ->
                                currentReward = reward
                                showRewardDialog = true
                                isSpinning = false
                                
                                // Update tickets
                                if (reward.type == "tickets") {
                                    userTickets = userTickets.copy(
                                        tickets = userTickets.tickets + reward.value
                                    )
                                }
                            }
                        )
                        
                        Spacer(modifier = Modifier.height(20.dp))
                        
                        Button(
                            onClick = {
                                if (userTickets.tickets >= 1 && !isSpinning) {
                                    userTickets = userTickets.copy(tickets = userTickets.tickets - 1)
                                    isSpinning = true
                                }
                            },
                            enabled = userTickets.tickets >= 1 && !isSpinning,
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(56.dp),
                            shape = RoundedCornerShape(12.dp)
                        ) {
                            if (isSpinning) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(24.dp),
                                    color = MaterialTheme.colorScheme.onPrimary,
                                    strokeWidth = 2.dp
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text("جاري الدوران...")
                            } else {
                                Icon(
                                    imageVector = Icons.Default.Star,
                                    contentDescription = null,
                                    modifier = Modifier.size(24.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = if (userTickets.tickets >= 1) "أدر العجلة (1 تذكرة)" else "تحتاج تذكرة",
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Medium
                                )
                            }
                        }
                    }
                }
            }
            
            // Daily Tasks
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(20.dp)
                    ) {
                        Text(
                            text = "المهام اليومية",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.SemiBold,
                            modifier = Modifier.padding(bottom = 16.dp)
                        )
                        
                        DailyTaskItem(
                            title = "إرسال 5 رسائل",
                            reward = "2 تذكرة",
                            isCompleted = true,
                            progress = 5,
                            total = 5
                        )
                        
                        Spacer(modifier = Modifier.height(12.dp))
                        
                        DailyTaskItem(
                            title = "قضاء 30 دقيقة في التطبيق",
                            reward = "3 تذاكر",
                            isCompleted = false,
                            progress = 18,
                            total = 30
                        )
                        
                        Spacer(modifier = Modifier.height(12.dp))
                        
                        DailyTaskItem(
                            title = "الإعجاب بـ 10 رسائل",
                            reward = "1 تذكرة",
                            isCompleted = false,
                            progress = 3,
                            total = 10
                        )
                    }
                }
            }
            
            // Rewards List
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(20.dp)
                    ) {
                        Text(
                            text = "الجوائز المتاحة",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.SemiBold,
                            modifier = Modifier.padding(bottom = 16.dp)
                        )
                        
                        rewards.forEach { reward ->
                            RewardItem(reward = reward)
                            if (reward != rewards.last()) {
                                Spacer(modifier = Modifier.height(8.dp))
                            }
                        }
                    }
                }
            }
        }
    }
    
    // Reward Dialog
    if (showRewardDialog && currentReward != null) {
        RewardDialog(
            reward = currentReward!!,
            onDismiss = { 
                showRewardDialog = false
                currentReward = null
            }
        )
    }
    
    // History Dialog
    if (showHistoryDialog) {
        SpinHistoryDialog(
            history = spinHistory,
            onDismiss = { showHistoryDialog = false }
        )
    }
}

@Composable
fun LuckyWheelComponent(
    rewards: List<LuckyWheelReward>,
    isSpinning: Boolean,
    onSpinComplete: (LuckyWheelReward) -> Unit
) {
    var rotation by remember { mutableStateOf(0f) }
    val animatedRotation by animateFloatAsState(
        targetValue = rotation,
        animationSpec = tween(
            durationMillis = if (isSpinning) 3000 else 0,
            easing = FastOutSlowInEasing
        ),
        finishedListener = {
            if (isSpinning) {
                // Calculate which reward was selected
                val normalizedRotation = (rotation % 360f + 360f) % 360f
                val sectionAngle = 360f / rewards.size
                val selectedIndex = ((360f - normalizedRotation) / sectionAngle).toInt() % rewards.size
                onSpinComplete(rewards[selectedIndex])
            }
        }
    )
    
    LaunchedEffect(isSpinning) {
        if (isSpinning) {
            rotation += 360f * 5 + Random.nextFloat() * 360f
        }
    }
    
    Box(
        modifier = Modifier.size(250.dp),
        contentAlignment = Alignment.Center
    ) {
        Canvas(
            modifier = Modifier
                .size(240.dp)
                .rotate(animatedRotation)
        ) {
            drawWheel(rewards)
        }
        
        // Center pointer
        Canvas(
            modifier = Modifier.size(30.dp)
        ) {
            val path = Path().apply {
                moveTo(size.center.x, 0f)
                lineTo(size.center.x - 15f, 30f)
                lineTo(size.center.x + 15f, 30f)
                close()
            }
            drawPath(
                path = path,
                color = Color.Red,
                style = Stroke(width = 2.dp.toPx())
            )
            drawPath(
                path = path,
                color = Color.White
            )
        }
    }
}

private fun DrawScope.drawWheel(rewards: List<LuckyWheelReward>) {
    val sectionAngle = 360f / rewards.size
    val radius = size.minDimension / 2
    
    rewards.forEachIndexed { index, reward ->
        val startAngle = index * sectionAngle
        val color = Color(android.graphics.Color.parseColor(reward.color))
        
        // Draw section
        drawArc(
            color = color,
            startAngle = startAngle,
            sweepAngle = sectionAngle,
            useCenter = true,
            size = size
        )
        
        // Draw border
        drawArc(
            color = Color.White,
            startAngle = startAngle,
            sweepAngle = sectionAngle,
            useCenter = true,
            size = size,
            style = Stroke(width = 2.dp.toPx())
        )
    }
}

@Composable
fun DailyTaskItem(
    title: String,
    reward: String,
    isCompleted: Boolean,
    progress: Int,
    total: Int
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )
            
            Text(
                text = "المكافأة: $reward",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.primary
            )
            
            if (!isCompleted) {
                LinearProgressIndicator(
                    progress = progress.toFloat() / total.toFloat(),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 4.dp),
                    color = MaterialTheme.colorScheme.primary
                )
                
                Text(
                    text = "$progress / $total",
                    fontSize = 10.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
        }
        
        if (isCompleted) {
            Icon(
                imageVector = Icons.Default.CheckCircle,
                contentDescription = "مكتملة",
                tint = MaterialTheme.colorScheme.primary
            )
        }
    }
}

@Composable
fun RewardItem(reward: LuckyWheelReward) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Surface(
                modifier = Modifier.size(12.dp),
                shape = CircleShape,
                color = Color(android.graphics.Color.parseColor(reward.color))
            ) {}
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Text(
                text = reward.name,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )
        }
        
        Text(
            text = "${(reward.probability * 100).toInt()}%",
            fontSize = 12.sp,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
        )
    }
}

@Composable
fun RewardDialog(
    reward: LuckyWheelReward,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "تهانينا! 🎉",
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        },
        text = {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "لقد ربحت:",
                    fontSize = 16.sp,
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = reward.name,
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary,
                    textAlign = TextAlign.Center
                )
                
                if (reward.description.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = reward.description,
                        fontSize = 14.sp,
                        textAlign = TextAlign.Center,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
            }
        },
        confirmButton = {
            Button(
                onClick = onDismiss,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("رائع!")
            }
        }
    )
}

@Composable
fun SpinHistoryDialog(
    history: List<WheelSpin>,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("سجل الدوران") },
        text = {
            LazyColumn {
                items(history) { spin ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = spin.rewardName,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium
                        )
                        
                        Text(
                            text = "منذ ${(System.currentTimeMillis() - spin.timestamp) / 3600000} س",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("إغلاق")
            }
        }
    )
}
