# دليل إعداد Trust Market Android

## نظرة عامة
تم تحويل تطبيق Trust Market من React Web App إلى Android App باستخدام Kotlin و Jetpack Compose.

## الملفات المُحوّلة من الموقع الأصلي

### من React إلى Kotlin/Compose:

1. **SignIn.jsx** → **SignInScreen.kt**
   - تسجيل الدخول بـ Google
   - واجهة مستخدم عربية
   - معالجة حالات التحميل

2. **Home/index.js** → **HomeScreen.kt**
   - عرض قائمة الغرف
   - إنشاء غرف جديدة
   - التنقل بين الشاشات

3. **chat_window** → **ChatScreen.kt**
   - واجهة الدردشة
   - إرسال الرسائل
   - عرض الرسائل

4. **firebase.config.js** → **FirebaseManager.kt**
   - إعدادات Firebase
   - إدارة المصادقة
   - عمليات قاعدة البيانات

## المميزات المُحوّلة

### ✅ مكتملة:
- [x] تسجيل الدخول بـ Google
- [x] واجهة المستخدم الأساسية
- [x] عرض الغرف
- [x] بنية الرسائل
- [x] دعم اللغة العربية
- [x] Material Design 3

### 🚧 قيد التطوير:
- [ ] الدردشة الفورية (Realtime)
- [ ] مشاركة الملفات
- [ ] الإشعارات
- [ ] الدردشة الخاصة
- [ ] عجلة الحظ
- [ ] لوحة الإدارة

## خطوات الإعداد التفصيلية

### 1. إعداد Firebase

#### إنشاء مشروع Firebase:
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. انقر على "Add project"
3. اسم المشروع: `trust-market-android`
4. فعّل Google Analytics (اختياري)

#### إضافة تطبيق Android:
1. انقر على أيقونة Android
2. Package name: `com.trustmarket.trust`
3. App nickname: `Trust Market`
4. SHA-1: احصل عليه من Android Studio أو باستخدام:
   ```bash
   keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
   ```

#### تحميل google-services.json:
1. حمّل الملف من Firebase Console
2. ضعه في مجلد `app/` في المشروع
3. تأكد من أن الملف يحتوي على package name الصحيح

### 2. تفعيل خدمات Firebase

#### Authentication:
1. اذهب إلى Authentication > Sign-in method
2. فعّل Google Sign-In
3. أضف SHA-1 fingerprint

#### Realtime Database:
1. اذهب إلى Realtime Database
2. انقر على "Create Database"
3. اختر موقع الخادم (us-central1 مُوصى به)
4. ابدأ في test mode
5. غيّر القواعد إلى:
```json
{
  "rules": {
    ".read": "auth != null",
    ".write": "auth != null",
    "users": {
      "$uid": {
        ".write": "$uid === auth.uid"
      }
    },
    "rooms": {
      ".indexOn": ["createdAt", "lastMessageTime"]
    },
    "messages": {
      "$roomId": {
        ".indexOn": ["timestamp"]
      }
    }
  }
}
```

#### Storage:
1. اذهب إلى Storage
2. انقر على "Get started"
3. غيّر القواعد إلى:
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /chat-files/{allPaths=**} {
      allow read, write: if request.auth != null 
        && resource.size < 5 * 1024 * 1024; // 5MB limit
    }
    match /user-avatars/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null 
        && request.auth.uid == userId;
    }
  }
}
```

### 3. بناء وتشغيل المشروع

#### من Android Studio:
1. افتح المشروع في Android Studio
2. انتظر حتى ينتهي Gradle sync
3. تأكد من وجود ملف `google-services.json` في مجلد `app/`
4. اختر جهاز أو محاكي
5. انقر على Run

#### من Command Line:
```bash
# تنظيف المشروع
./gradlew clean

# بناء المشروع
./gradlew build

# تثبيت على الجهاز
./gradlew installDebug

# أو استخدم الـ script المُعد مسبقاً
./build_and_run.bat
```

### 4. اختبار التطبيق

#### اختبار تسجيل الدخول:
1. افتح التطبيق
2. انقر على "تسجيل الدخول بحساب Google"
3. اختر حساب Google
4. تأكد من الانتقال إلى الشاشة الرئيسية

#### اختبار الواجهة:
1. تحقق من عرض الغرف الوهمية
2. جرب إنشاء غرفة جديدة
3. تأكد من عمل التنقل بين الشاشات

## استكشاف الأخطاء

### مشاكل شائعة:

#### خطأ في google-services.json:
```
Error: File google-services.json is missing
```
**الحل**: تأكد من وجود الملف في مجلد `app/` وأن package name صحيح

#### خطأ في SHA-1:
```
Error: This app is not authorized to use Firebase Authentication
```
**الحل**: أضف SHA-1 fingerprint في Firebase Console

#### خطأ في البناء:
```
Error: Could not resolve com.google.firebase:firebase-bom
```
**الحل**: تأكد من اتصال الإنترنت وتحديث Android Studio

### سجلات التطبيق:
```bash
# عرض سجلات التطبيق
adb logcat | grep "TrustMarket"

# عرض سجلات Firebase
adb logcat | grep "Firebase"
```

## الخطوات التالية

### المرحلة التالية من التطوير:
1. **تنفيذ الدردشة الفورية**
   - ربط FirebaseManager بالواجهة
   - معالجة الرسائل الفورية
   - تحديث قائمة الرسائل

2. **إضافة مشاركة الملفات**
   - اختيار الملفات من الجهاز
   - رفع إلى Firebase Storage
   - عرض الملفات في الدردشة

3. **تنفيذ الإشعارات**
   - Firebase Cloud Messaging
   - إشعارات محلية
   - معالجة الإشعارات

### ملاحظات للمطور:
- جميع النصوص باللغة العربية
- التصميم يدعم RTL
- الكود منظم ومُعلّق
- يتبع أفضل ممارسات Android

---

*تم إنشاء هذا الدليل بواسطة Augment Agent*
