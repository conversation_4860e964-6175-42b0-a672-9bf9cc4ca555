package com.trustmarket.trust

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.lifecycle.viewmodel.compose.viewModel
import com.google.firebase.auth.FirebaseAuth
import com.trustmarket.trust.ui.screens.HomeScreen
import com.trustmarket.trust.ui.screens.SignInScreen
import com.trustmarket.trust.ui.theme.TrustTheme
import com.trustmarket.trust.data.models.Room

class MainActivity : ComponentActivity() {
    private lateinit var auth: FirebaseAuth

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        auth = FirebaseAuth.getInstance()

        enableEdgeToEdge()
        setContent {
            TrustTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    TrustMarketApp()
                }
            }
        }
    }
}

@Composable
fun TrustMarketApp() {
    var isSignedIn by remember { mutableStateOf(false) }
    var isLoading by remember { mutableStateOf(false) }

    // Mock data for now
    val mockRooms = remember {
        listOf(
            Room(
                id = "1",
                name = "الغرفة العامة",
                description = "مرحباً بكم في الغرفة العامة للدردشة",
                currentUsers = 15,
                lastMessage = "مرحباً بالجميع",
                lastMessageAuthor = "أحمد",
                lastMessageTime = System.currentTimeMillis() - 300000,
                category = "general"
            ),
            Room(
                id = "2",
                name = "التقنية والبرمجة",
                description = "نقاش حول التقنية والبرمجة",
                currentUsers = 8,
                lastMessage = "ما رأيكم في Kotlin؟",
                lastMessageAuthor = "سارة",
                lastMessageTime = System.currentTimeMillis() - 600000,
                category = "tech"
            )
        )
    }

    if (isSignedIn) {
        HomeScreen(
            rooms = mockRooms,
            onRoomClick = { room ->
                // Navigate to chat screen
            },
            onCreateRoom = {
                // Handle create room
            },
            onPrivateChats = {
                // Navigate to private chats
            },
            onSettings = {
                // Navigate to settings
            },
            onSignOut = {
                isSignedIn = false
            }
        )
    } else {
        SignInScreen(
            onGoogleSignIn = {
                isLoading = true
                // Simulate sign in
                isSignedIn = true
                isLoading = false
            },
            isLoading = isLoading
        )
    }
}