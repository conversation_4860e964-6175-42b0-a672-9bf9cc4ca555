package com.trustmarket.trust.data.models

import com.google.firebase.database.PropertyName

data class Room(
    @PropertyName("id")
    val id: String = "",
    
    @PropertyName("name")
    val name: String = "",
    
    @PropertyName("description")
    val description: String = "",
    
    @PropertyName("createdAt")
    val createdAt: Long = 0L,
    
    @PropertyName("createdBy")
    val createdBy: String = "",
    
    @PropertyName("isPrivate")
    val isPrivate: Boolean = false,
    
    @PropertyName("password")
    val password: String = "",
    
    @PropertyName("maxUsers")
    val maxUsers: Int = 100,
    
    @PropertyName("currentUsers")
    val currentUsers: Int = 0,
    
    @PropertyName("members")
    val members: Map<String, Boolean> = emptyMap(),
    
    @PropertyName("admins")
    val admins: Map<String, Boolean> = emptyMap(),
    
    @PropertyName("bannedUsers")
    val bannedUsers: Map<String, Boolean> = emptyMap(),
    
    @PropertyName("lastMessage")
    val lastMessage: String = "",
    
    @PropertyName("lastMessageTime")
    val lastMessageTime: Long = 0L,
    
    @PropertyName("lastMessageAuthor")
    val lastMessageAuthor: String = "",
    
    @PropertyName("isDeleted")
    val isDeleted: Boolean = false,
    
    @PropertyName("category")
    val category: String = "general"
) {
    // Constructor بدون معاملات مطلوب لـ Firebase
    constructor() : this("", "", "", 0L, "", false, "", 100, 0, emptyMap(), emptyMap(), emptyMap(), "", 0L, "", false, "general")
}
