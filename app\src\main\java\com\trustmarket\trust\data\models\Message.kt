package com.trustmarket.trust.data.models

import com.google.firebase.database.PropertyName

data class Message(
    @PropertyName("id")
    val id: String = "",
    
    @PropertyName("text")
    val text: String = "",
    
    @PropertyName("authorId")
    val authorId: String = "",
    
    @PropertyName("authorName")
    val authorName: String = "",
    
    @PropertyName("authorAvatar")
    val authorAvatar: String = "",
    
    @PropertyName("timestamp")
    val timestamp: Long = 0L,
    
    @PropertyName("type")
    val type: String = "text", // text, image, file, audio
    
    @PropertyName("fileUrl")
    val fileUrl: String = "",
    
    @PropertyName("fileName")
    val fileName: String = "",
    
    @PropertyName("fileSize")
    val fileSize: Long = 0L,
    
    @PropertyName("roomId")
    val roomId: String = "",
    
    @PropertyName("isPrivate")
    val isPrivate: Boolean = false,
    
    @PropertyName("recipientId")
    val recipientId: String = "",
    
    @PropertyName("likes")
    val likes: Map<String, Boolean> = emptyMap(),
    
    @PropertyName("isDeleted")
    val isDeleted: Boolean = false,
    
    @PropertyName("mentions")
    val mentions: List<String> = emptyList()
) {
    // Constructor بدون معاملات مطلوب لـ Firebase
    constructor() : this("", "", "", "", "", 0L, "text", "", "", 0L, "", false, "", emptyMap(), false, emptyList())
}
