{"logs": [{"outputFile": "com.trustmarket.trust.app-mergeDebugResources-70:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\27e7e2f5ae47ff80d1406eee848a3aa7\\transformed\\ui-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,297,391,490,580,659,752,847,932,1013,1099,1172,1249,1328,1405,1484,1554", "endColumns": "104,86,93,98,89,78,92,94,84,80,85,72,76,78,76,78,69,117", "endOffsets": "205,292,386,485,575,654,747,842,927,1008,1094,1167,1244,1323,1400,1479,1549,1667"}, "to": {"startLines": "11,12,32,33,34,38,39,97,98,99,100,101,102,103,104,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1064,1169,3548,3642,3741,4147,4226,10626,10721,10806,10887,10973,11046,11123,11202,11380,11459,11529", "endColumns": "104,86,93,98,89,78,92,94,84,80,85,72,76,78,76,78,69,117", "endOffsets": "1164,1251,3637,3736,3826,4221,4314,10716,10801,10882,10968,11041,11118,11197,11274,11454,11524,11642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9cef95d772ca94c574c1d7981123fcc6\\transformed\\play-services-base-18.5.0\\res\\values-hr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,448,574,680,833,959,1070,1173,1319,1422,1575,1699,1842,1981,2045,2103", "endColumns": "101,152,125,105,152,125,110,102,145,102,152,123,142,138,63,57,76", "endOffsets": "294,447,573,679,832,958,1069,1172,1318,1421,1574,1698,1841,1980,2044,2102,2179"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1256,1362,1519,1649,1759,1916,2046,2161,2400,2550,2657,2814,2942,3089,3232,3300,3362", "endColumns": "105,156,129,109,156,129,114,106,149,106,156,127,146,142,67,61,80", "endOffsets": "1357,1514,1644,1754,1911,2041,2156,2263,2545,2652,2809,2937,3084,3227,3295,3357,3438"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a4406fb22280fb302c67f992ef8133c\\transformed\\material3-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,296,416,537,637,731,842,983,1102,1247,1332,1432,1527,1625,1744,1870,1975,2111,2246,2380,2548,2674,2798,2926,3050,3146,3244,3374,3508,3605,3707,3816,3957,4104,4213,4313,4398,4491,4586,4699,4793,4879,4988,5076,5159,5256,5357,5450,5547,5635,5743,5840,5942,6080,6170,6270", "endColumns": "119,120,119,120,99,93,110,140,118,144,84,99,94,97,118,125,104,135,134,133,167,125,123,127,123,95,97,129,133,96,101,108,140,146,108,99,84,92,94,112,93,85,108,87,82,96,100,92,96,87,107,96,101,137,89,99,91", "endOffsets": "170,291,411,532,632,726,837,978,1097,1242,1327,1427,1522,1620,1739,1865,1970,2106,2241,2375,2543,2669,2793,2921,3045,3141,3239,3369,3503,3600,3702,3811,3952,4099,4208,4308,4393,4486,4581,4694,4788,4874,4983,5071,5154,5251,5352,5445,5542,5630,5738,5835,5937,6075,6165,6265,6357"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4319,4439,4560,4680,4801,4901,4995,5106,5247,5366,5511,5596,5696,5791,5889,6008,6134,6239,6375,6510,6644,6812,6938,7062,7190,7314,7410,7508,7638,7772,7869,7971,8080,8221,8368,8477,8577,8662,8755,8850,8963,9057,9143,9252,9340,9423,9520,9621,9714,9811,9899,10007,10104,10206,10344,10434,10534", "endColumns": "119,120,119,120,99,93,110,140,118,144,84,99,94,97,118,125,104,135,134,133,167,125,123,127,123,95,97,129,133,96,101,108,140,146,108,99,84,92,94,112,93,85,108,87,82,96,100,92,96,87,107,96,101,137,89,99,91", "endOffsets": "4434,4555,4675,4796,4896,4990,5101,5242,5361,5506,5591,5691,5786,5884,6003,6129,6234,6370,6505,6639,6807,6933,7057,7185,7309,7405,7503,7633,7767,7864,7966,8075,8216,8363,8472,8572,8657,8750,8845,8958,9052,9138,9247,9335,9418,9515,9616,9709,9806,9894,10002,10099,10201,10339,10429,10529,10621"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9f3b0e192fae8d81415bbd16b5199dc2\\transformed\\browser-1.4.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,374", "endColumns": "104,99,113,101", "endOffsets": "155,255,369,471"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3443,3831,3931,4045", "endColumns": "104,99,113,101", "endOffsets": "3543,3926,4040,4142"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\63ccc495eef38375f13c6ff523cfbb5a\\transformed\\play-services-basement-18.5.0\\res\\values-hr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2268", "endColumns": "131", "endOffsets": "2395"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\34a6171889a3f0a2e7cbf13a2f1c0e80\\transformed\\credentials-1.2.0-rc01\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,121", "endOffsets": "161,283"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,216", "endColumns": "110,121", "endOffsets": "211,333"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e535ebbc8274cd26275a634ce7617c3d\\transformed\\core-1.16.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "4,5,6,7,8,9,10,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "338,436,543,640,739,843,947,11279", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "431,538,635,734,838,942,1059,11375"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b44500860a60172f953a4f023e793d5e\\transformed\\foundation-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,84", "endOffsets": "133,218"}, "to": {"startLines": "109,110", "startColumns": "4,4", "startOffsets": "11647,11730", "endColumns": "82,84", "endOffsets": "11725,11810"}}]}]}