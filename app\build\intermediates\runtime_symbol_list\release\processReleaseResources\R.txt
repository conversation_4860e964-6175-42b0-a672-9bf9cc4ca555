int anim fragment_fast_out_extra_slow_in 0x7f010000
int animator fragment_close_enter 0x7f020000
int animator fragment_close_exit 0x7f020001
int animator fragment_fade_enter 0x7f020002
int animator fragment_fade_exit 0x7f020003
int animator fragment_open_enter 0x7f020004
int animator fragment_open_exit 0x7f020005
int attr action 0x7f030000
int attr alpha 0x7f030001
int attr argType 0x7f030002
int attr buttonSize 0x7f030003
int attr circleCrop 0x7f030004
int attr colorScheme 0x7f030005
int attr data 0x7f030006
int attr dataPattern 0x7f030007
int attr destination 0x7f030008
int attr enterAnim 0x7f030009
int attr exitAnim 0x7f03000a
int attr font 0x7f03000b
int attr fontProviderAuthority 0x7f03000c
int attr fontProviderCerts 0x7f03000d
int attr fontProviderFallbackQuery 0x7f03000e
int attr fontProviderFetchStrategy 0x7f03000f
int attr fontProviderFetchTimeout 0x7f030010
int attr fontProviderPackage 0x7f030011
int attr fontProviderQuery 0x7f030012
int attr fontProviderSystemFontFamily 0x7f030013
int attr fontStyle 0x7f030014
int attr fontVariationSettings 0x7f030015
int attr fontWeight 0x7f030016
int attr graph 0x7f030017
int attr imageAspectRatio 0x7f030018
int attr imageAspectRatioAdjust 0x7f030019
int attr lStar 0x7f03001a
int attr launchSingleTop 0x7f03001b
int attr mimeType 0x7f03001c
int attr navGraph 0x7f03001d
int attr nestedScrollViewStyle 0x7f03001e
int attr nullable 0x7f03001f
int attr popEnterAnim 0x7f030020
int attr popExitAnim 0x7f030021
int attr popUpTo 0x7f030022
int attr popUpToInclusive 0x7f030023
int attr popUpToSaveState 0x7f030024
int attr queryPatterns 0x7f030025
int attr restoreState 0x7f030026
int attr route 0x7f030027
int attr scopeUris 0x7f030028
int attr shortcutMatchRequired 0x7f030029
int attr startDestination 0x7f03002a
int attr targetPackage 0x7f03002b
int attr ttcIndex 0x7f03002c
int attr uri 0x7f03002d
int color androidx_core_ripple_material_light 0x7f040000
int color androidx_core_secondary_text_default_material_light 0x7f040001
int color black 0x7f040002
int color browser_actions_bg_grey 0x7f040003
int color browser_actions_divider_color 0x7f040004
int color browser_actions_text_color 0x7f040005
int color browser_actions_title_color 0x7f040006
int color call_notification_answer_color 0x7f040007
int color call_notification_decline_color 0x7f040008
int color common_google_signin_btn_text_dark 0x7f040009
int color common_google_signin_btn_text_dark_default 0x7f04000a
int color common_google_signin_btn_text_dark_disabled 0x7f04000b
int color common_google_signin_btn_text_dark_focused 0x7f04000c
int color common_google_signin_btn_text_dark_pressed 0x7f04000d
int color common_google_signin_btn_text_light 0x7f04000e
int color common_google_signin_btn_text_light_default 0x7f04000f
int color common_google_signin_btn_text_light_disabled 0x7f040010
int color common_google_signin_btn_text_light_focused 0x7f040011
int color common_google_signin_btn_text_light_pressed 0x7f040012
int color common_google_signin_btn_tint 0x7f040013
int color notification_action_color_filter 0x7f040014
int color notification_icon_bg_color 0x7f040015
int color purple_200 0x7f040016
int color purple_500 0x7f040017
int color purple_700 0x7f040018
int color teal_200 0x7f040019
int color teal_700 0x7f04001a
int color vector_tint_color 0x7f04001b
int color vector_tint_theme_color 0x7f04001c
int color white 0x7f04001d
int dimen browser_actions_context_menu_max_width 0x7f050000
int dimen browser_actions_context_menu_min_padding 0x7f050001
int dimen compat_button_inset_horizontal_material 0x7f050002
int dimen compat_button_inset_vertical_material 0x7f050003
int dimen compat_button_padding_horizontal_material 0x7f050004
int dimen compat_button_padding_vertical_material 0x7f050005
int dimen compat_control_corner_material 0x7f050006
int dimen compat_notification_large_icon_max_height 0x7f050007
int dimen compat_notification_large_icon_max_width 0x7f050008
int dimen notification_action_icon_size 0x7f050009
int dimen notification_action_text_size 0x7f05000a
int dimen notification_big_circle_margin 0x7f05000b
int dimen notification_content_margin_start 0x7f05000c
int dimen notification_large_icon_height 0x7f05000d
int dimen notification_large_icon_width 0x7f05000e
int dimen notification_main_column_padding_top 0x7f05000f
int dimen notification_media_narrow_margin 0x7f050010
int dimen notification_right_icon_size 0x7f050011
int dimen notification_right_side_padding_top 0x7f050012
int dimen notification_small_icon_background_padding 0x7f050013
int dimen notification_small_icon_size_as_large 0x7f050014
int dimen notification_subtext_size 0x7f050015
int dimen notification_top_pad 0x7f050016
int dimen notification_top_pad_large_text 0x7f050017
int drawable abc_vector_test 0x7f060001
int drawable common_full_open_on_phone 0x7f060002
int drawable common_google_signin_btn_icon_dark 0x7f060003
int drawable common_google_signin_btn_icon_dark_focused 0x7f060004
int drawable common_google_signin_btn_icon_dark_normal 0x7f060005
int drawable common_google_signin_btn_icon_dark_normal_background 0x7f060006
int drawable common_google_signin_btn_icon_disabled 0x7f060007
int drawable common_google_signin_btn_icon_light 0x7f060008
int drawable common_google_signin_btn_icon_light_focused 0x7f060009
int drawable common_google_signin_btn_icon_light_normal 0x7f06000a
int drawable common_google_signin_btn_icon_light_normal_background 0x7f06000b
int drawable common_google_signin_btn_text_dark 0x7f06000c
int drawable common_google_signin_btn_text_dark_focused 0x7f06000d
int drawable common_google_signin_btn_text_dark_normal 0x7f06000e
int drawable common_google_signin_btn_text_dark_normal_background 0x7f06000f
int drawable common_google_signin_btn_text_disabled 0x7f060010
int drawable common_google_signin_btn_text_light 0x7f060011
int drawable common_google_signin_btn_text_light_focused 0x7f060012
int drawable common_google_signin_btn_text_light_normal 0x7f060013
int drawable common_google_signin_btn_text_light_normal_background 0x7f060014
int drawable googleg_disabled_color_18 0x7f060015
int drawable googleg_standard_color_18 0x7f060016
int drawable ic_call_answer 0x7f060017
int drawable ic_call_answer_low 0x7f060018
int drawable ic_call_answer_video 0x7f060019
int drawable ic_call_answer_video_low 0x7f06001a
int drawable ic_call_decline 0x7f06001b
int drawable ic_call_decline_low 0x7f06001c
int drawable ic_launcher_background 0x7f06001d
int drawable ic_launcher_foreground 0x7f06001e
int drawable ic_other_sign_in 0x7f06001f
int drawable ic_passkey 0x7f060020
int drawable ic_password 0x7f060021
int drawable notification_action_background 0x7f060022
int drawable notification_bg 0x7f060023
int drawable notification_bg_low 0x7f060024
int drawable notification_bg_low_normal 0x7f060025
int drawable notification_bg_low_pressed 0x7f060026
int drawable notification_bg_normal 0x7f060027
int drawable notification_bg_normal_pressed 0x7f060028
int drawable notification_icon_background 0x7f060029
int drawable notification_oversize_large_icon_bg 0x7f06002a
int drawable notification_template_icon_bg 0x7f06002b
int drawable notification_template_icon_low_bg 0x7f06002c
int drawable notification_tile_bg 0x7f06002d
int drawable notify_panel_notification_icon_bg 0x7f06002e
int id accessibility_action_clickable_span 0x7f070000
int id accessibility_custom_action_0 0x7f070001
int id accessibility_custom_action_1 0x7f070002
int id accessibility_custom_action_10 0x7f070003
int id accessibility_custom_action_11 0x7f070004
int id accessibility_custom_action_12 0x7f070005
int id accessibility_custom_action_13 0x7f070006
int id accessibility_custom_action_14 0x7f070007
int id accessibility_custom_action_15 0x7f070008
int id accessibility_custom_action_16 0x7f070009
int id accessibility_custom_action_17 0x7f07000a
int id accessibility_custom_action_18 0x7f07000b
int id accessibility_custom_action_19 0x7f07000c
int id accessibility_custom_action_2 0x7f07000d
int id accessibility_custom_action_20 0x7f07000e
int id accessibility_custom_action_21 0x7f07000f
int id accessibility_custom_action_22 0x7f070010
int id accessibility_custom_action_23 0x7f070011
int id accessibility_custom_action_24 0x7f070012
int id accessibility_custom_action_25 0x7f070013
int id accessibility_custom_action_26 0x7f070014
int id accessibility_custom_action_27 0x7f070015
int id accessibility_custom_action_28 0x7f070016
int id accessibility_custom_action_29 0x7f070017
int id accessibility_custom_action_3 0x7f070018
int id accessibility_custom_action_30 0x7f070019
int id accessibility_custom_action_31 0x7f07001a
int id accessibility_custom_action_4 0x7f07001b
int id accessibility_custom_action_5 0x7f07001c
int id accessibility_custom_action_6 0x7f07001d
int id accessibility_custom_action_7 0x7f07001e
int id accessibility_custom_action_8 0x7f07001f
int id accessibility_custom_action_9 0x7f070020
int id action_container 0x7f070021
int id action_divider 0x7f070022
int id action_image 0x7f070023
int id action_text 0x7f070024
int id actions 0x7f070025
int id adjust_height 0x7f070026
int id adjust_width 0x7f070027
int id androidx_compose_ui_view_composition_context 0x7f070028
int id async 0x7f070029
int id auto 0x7f07002a
int id blocking 0x7f07002b
int id browser_actions_header_text 0x7f07002c
int id browser_actions_menu_item_icon 0x7f07002d
int id browser_actions_menu_item_text 0x7f07002e
int id browser_actions_menu_items 0x7f07002f
int id browser_actions_menu_view 0x7f070030
int id chronometer 0x7f070031
int id coil_request_manager 0x7f070032
int id compose_view_saveable_id_tag 0x7f070033
int id consume_window_insets_tag 0x7f070034
int id dark 0x7f070035
int id dialog_button 0x7f070036
int id edit_text_id 0x7f070037
int id forever 0x7f070038
int id fragment_container_view_tag 0x7f070039
int id hide_graphics_layer_in_inspector_tag 0x7f07003a
int id hide_ime_id 0x7f07003b
int id hide_in_inspector_tag 0x7f07003c
int id icon 0x7f07003d
int id icon_group 0x7f07003e
int id icon_only 0x7f07003f
int id info 0x7f070040
int id inspection_slot_table_set 0x7f070041
int id is_pooling_container_tag 0x7f070042
int id italic 0x7f070043
int id light 0x7f070044
int id line1 0x7f070045
int id line3 0x7f070046
int id nav_controller_view_tag 0x7f070047
int id none 0x7f070048
int id normal 0x7f070049
int id notification_background 0x7f07004a
int id notification_main_column 0x7f07004b
int id notification_main_column_container 0x7f07004c
int id pooling_container_listener_holder_tag 0x7f07004d
int id report_drawn 0x7f07004e
int id right_icon 0x7f07004f
int id right_side 0x7f070050
int id special_effects_controller_view_tag 0x7f070051
int id standard 0x7f070052
int id tag_accessibility_actions 0x7f070053
int id tag_accessibility_clickable_spans 0x7f070054
int id tag_accessibility_heading 0x7f070055
int id tag_accessibility_pane_title 0x7f070056
int id tag_compat_insets_dispatch 0x7f070057
int id tag_on_apply_window_listener 0x7f070058
int id tag_on_receive_content_listener 0x7f070059
int id tag_on_receive_content_mime_types 0x7f07005a
int id tag_screen_reader_focusable 0x7f07005b
int id tag_state_description 0x7f07005c
int id tag_system_bar_state_monitor 0x7f07005d
int id tag_transition_group 0x7f07005e
int id tag_unhandled_key_event_manager 0x7f07005f
int id tag_unhandled_key_listeners 0x7f070060
int id tag_window_insets_animation_callback 0x7f070061
int id text 0x7f070062
int id text2 0x7f070063
int id time 0x7f070064
int id title 0x7f070065
int id view_tree_disjoint_parent 0x7f070066
int id view_tree_lifecycle_owner 0x7f070067
int id view_tree_on_back_pressed_dispatcher_owner 0x7f070068
int id view_tree_saved_state_registry_owner 0x7f070069
int id view_tree_view_model_store_owner 0x7f07006a
int id visible_removing_fragment_view_tag 0x7f07006b
int id wide 0x7f07006c
int id wrapped_composition_tag 0x7f07006d
int integer google_play_services_version 0x7f080000
int integer m3c_window_layout_in_display_cutout_mode 0x7f080001
int integer status_bar_notification_info_maxnum 0x7f080002
int layout browser_actions_context_menu_page 0x7f090000
int layout browser_actions_context_menu_row 0x7f090001
int layout custom_dialog 0x7f090002
int layout ime_base_split_test_activity 0x7f090003
int layout ime_secondary_split_test_activity 0x7f090004
int layout notification_action 0x7f090005
int layout notification_action_tombstone 0x7f090006
int layout notification_template_custom_big 0x7f090007
int layout notification_template_icon_group 0x7f090008
int layout notification_template_part_chronometer 0x7f090009
int layout notification_template_part_time 0x7f09000a
int mipmap ic_launcher 0x7f0a0000
int mipmap ic_launcher_round 0x7f0a0001
int raw firebase_common_keep 0x7f0b0000
int string android_credentials_TYPE_PASSWORD_CREDENTIAL 0x7f0c0000
int string androidx_credentials_TYPE_PUBLIC_KEY_CREDENTIAL 0x7f0c0001
int string androidx_startup 0x7f0c0002
int string app_name 0x7f0c0003
int string attach_file 0x7f0c0004
int string audio_message 0x7f0c0005
int string back 0x7f0c0006
int string call_notification_answer_action 0x7f0c0007
int string call_notification_answer_video_action 0x7f0c0008
int string call_notification_decline_action 0x7f0c0009
int string call_notification_hang_up_action 0x7f0c000a
int string call_notification_incoming_text 0x7f0c000b
int string call_notification_ongoing_text 0x7f0c000c
int string call_notification_screening_text 0x7f0c000d
int string cancel 0x7f0c000e
int string chat_rooms 0x7f0c000f
int string close_drawer 0x7f0c0010
int string close_sheet 0x7f0c0011
int string common_google_play_services_enable_button 0x7f0c0012
int string common_google_play_services_enable_text 0x7f0c0013
int string common_google_play_services_enable_title 0x7f0c0014
int string common_google_play_services_install_button 0x7f0c0015
int string common_google_play_services_install_text 0x7f0c0016
int string common_google_play_services_install_title 0x7f0c0017
int string common_google_play_services_notification_channel_name 0x7f0c0018
int string common_google_play_services_notification_ticker 0x7f0c0019
int string common_google_play_services_unknown_issue 0x7f0c001a
int string common_google_play_services_unsupported_text 0x7f0c001b
int string common_google_play_services_update_button 0x7f0c001c
int string common_google_play_services_update_text 0x7f0c001d
int string common_google_play_services_update_title 0x7f0c001e
int string common_google_play_services_updating_text 0x7f0c001f
int string common_google_play_services_wear_update_text 0x7f0c0020
int string common_open_on_phone 0x7f0c0021
int string common_signin_button_text 0x7f0c0022
int string common_signin_button_text_long 0x7f0c0023
int string copy_toast_msg 0x7f0c0024
int string create 0x7f0c0025
int string create_room 0x7f0c0026
int string create_room_hint 0x7f0c0027
int string default_error_message 0x7f0c0028
int string default_popup_window_title 0x7f0c0029
int string default_web_client_id 0x7f0c002a
int string delete 0x7f0c002b
int string dropdown_menu 0x7f0c002c
int string fallback_menu_item_copy_link 0x7f0c002d
int string fallback_menu_item_open_in_browser 0x7f0c002e
int string fallback_menu_item_share_link 0x7f0c002f
int string feature_easy_ui 0x7f0c0030
int string feature_file_sharing 0x7f0c0031
int string feature_group_chat 0x7f0c0032
int string feature_notifications 0x7f0c0033
int string feature_private_chat 0x7f0c0034
int string features 0x7f0c0035
int string firebase_database_url 0x7f0c0036
int string gcm_defaultSenderId 0x7f0c0037
int string google_api_key 0x7f0c0038
int string google_app_id 0x7f0c0039
int string google_crash_reporting_api_key 0x7f0c003a
int string google_storage_bucket 0x7f0c003b
int string image_message 0x7f0c003c
int string in_progress 0x7f0c003d
int string indeterminate 0x7f0c003e
int string like 0x7f0c003f
int string m3c_bottom_sheet_collapse_description 0x7f0c0040
int string m3c_bottom_sheet_dismiss_description 0x7f0c0041
int string m3c_bottom_sheet_drag_handle_description 0x7f0c0042
int string m3c_bottom_sheet_expand_description 0x7f0c0043
int string m3c_bottom_sheet_pane_title 0x7f0c0044
int string m3c_date_input_headline 0x7f0c0045
int string m3c_date_input_headline_description 0x7f0c0046
int string m3c_date_input_invalid_for_pattern 0x7f0c0047
int string m3c_date_input_invalid_not_allowed 0x7f0c0048
int string m3c_date_input_invalid_year_range 0x7f0c0049
int string m3c_date_input_label 0x7f0c004a
int string m3c_date_input_no_input_description 0x7f0c004b
int string m3c_date_input_title 0x7f0c004c
int string m3c_date_picker_headline 0x7f0c004d
int string m3c_date_picker_headline_description 0x7f0c004e
int string m3c_date_picker_navigate_to_year_description 0x7f0c004f
int string m3c_date_picker_no_selection_description 0x7f0c0050
int string m3c_date_picker_scroll_to_earlier_years 0x7f0c0051
int string m3c_date_picker_scroll_to_later_years 0x7f0c0052
int string m3c_date_picker_switch_to_calendar_mode 0x7f0c0053
int string m3c_date_picker_switch_to_day_selection 0x7f0c0054
int string m3c_date_picker_switch_to_input_mode 0x7f0c0055
int string m3c_date_picker_switch_to_next_month 0x7f0c0056
int string m3c_date_picker_switch_to_previous_month 0x7f0c0057
int string m3c_date_picker_switch_to_year_selection 0x7f0c0058
int string m3c_date_picker_title 0x7f0c0059
int string m3c_date_picker_today_description 0x7f0c005a
int string m3c_date_picker_year_picker_pane_title 0x7f0c005b
int string m3c_date_range_input_invalid_range_input 0x7f0c005c
int string m3c_date_range_input_title 0x7f0c005d
int string m3c_date_range_picker_day_in_range 0x7f0c005e
int string m3c_date_range_picker_end_headline 0x7f0c005f
int string m3c_date_range_picker_scroll_to_next_month 0x7f0c0060
int string m3c_date_range_picker_scroll_to_previous_month 0x7f0c0061
int string m3c_date_range_picker_start_headline 0x7f0c0062
int string m3c_date_range_picker_title 0x7f0c0063
int string m3c_dialog 0x7f0c0064
int string m3c_dropdown_menu_collapsed 0x7f0c0065
int string m3c_dropdown_menu_expanded 0x7f0c0066
int string m3c_dropdown_menu_toggle 0x7f0c0067
int string m3c_search_bar_search 0x7f0c0068
int string m3c_snackbar_dismiss 0x7f0c0069
int string m3c_suggestions_available 0x7f0c006a
int string m3c_time_picker_am 0x7f0c006b
int string m3c_time_picker_hour 0x7f0c006c
int string m3c_time_picker_hour_24h_suffix 0x7f0c006d
int string m3c_time_picker_hour_selection 0x7f0c006e
int string m3c_time_picker_hour_suffix 0x7f0c006f
int string m3c_time_picker_hour_text_field 0x7f0c0070
int string m3c_time_picker_minute 0x7f0c0071
int string m3c_time_picker_minute_selection 0x7f0c0072
int string m3c_time_picker_minute_suffix 0x7f0c0073
int string m3c_time_picker_minute_text_field 0x7f0c0074
int string m3c_time_picker_period_toggle_description 0x7f0c0075
int string m3c_time_picker_pm 0x7f0c0076
int string m3c_tooltip_long_press_label 0x7f0c0077
int string m3c_tooltip_pane_description 0x7f0c0078
int string navigation_menu 0x7f0c0079
int string no_rooms 0x7f0c007a
int string not_selected 0x7f0c007b
int string private_chats 0x7f0c007c
int string project_id 0x7f0c007d
int string range_end 0x7f0c007e
int string range_start 0x7f0c007f
int string room_description 0x7f0c0080
int string room_info 0x7f0c0081
int string room_name 0x7f0c0082
int string selected 0x7f0c0083
int string send 0x7f0c0084
int string settings 0x7f0c0085
int string sign_in_description 0x7f0c0086
int string sign_in_subtitle 0x7f0c0087
int string sign_in_title 0x7f0c0088
int string sign_in_welcome 0x7f0c0089
int string sign_in_with_google 0x7f0c008a
int string sign_out 0x7f0c008b
int string state_empty 0x7f0c008c
int string state_off 0x7f0c008d
int string state_on 0x7f0c008e
int string status_bar_notification_info_overflow 0x7f0c008f
int string switch_role 0x7f0c0090
int string tab 0x7f0c0091
int string template_percent 0x7f0c0092
int string tooltip_description 0x7f0c0093
int string tooltip_label 0x7f0c0094
int string type_message 0x7f0c0095
int string users_online 0x7f0c0096
int style DialogWindowTheme 0x7f0d0000
int style EdgeToEdgeFloatingDialogTheme 0x7f0d0001
int style EdgeToEdgeFloatingDialogWindowTheme 0x7f0d0002
int style FloatingDialogTheme 0x7f0d0003
int style FloatingDialogWindowTheme 0x7f0d0004
int style TextAppearance_Compat_Notification 0x7f0d0005
int style TextAppearance_Compat_Notification_Info 0x7f0d0006
int style TextAppearance_Compat_Notification_Line2 0x7f0d0007
int style TextAppearance_Compat_Notification_Time 0x7f0d0008
int style TextAppearance_Compat_Notification_Title 0x7f0d0009
int style Theme_Hidden 0x7f0d000a
int style Theme_PlayCore_Transparent 0x7f0d000b
int style Theme_Trust 0x7f0d000c
int style Widget_Compat_NotificationActionContainer 0x7f0d000d
int style Widget_Compat_NotificationActionText 0x7f0d000e
int[] styleable ActivityNavigator { 0x01010003, 0x7f030000, 0x7f030006, 0x7f030007, 0x7f03002b }
int styleable ActivityNavigator_android_name 0
int styleable ActivityNavigator_action 1
int styleable ActivityNavigator_data 2
int styleable ActivityNavigator_dataPattern 3
int styleable ActivityNavigator_targetPackage 4
int[] styleable AnimatedStateListDrawableCompat { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable AnimatedStateListDrawableCompat_android_dither 0
int styleable AnimatedStateListDrawableCompat_android_visible 1
int styleable AnimatedStateListDrawableCompat_android_variablePadding 2
int styleable AnimatedStateListDrawableCompat_android_constantSize 3
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 4
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 5
int[] styleable AnimatedStateListDrawableItem { 0x010100d0, 0x01010199 }
int styleable AnimatedStateListDrawableItem_android_id 0
int styleable AnimatedStateListDrawableItem_android_drawable 1
int[] styleable AnimatedStateListDrawableTransition { 0x01010199, 0x01010449, 0x0101044a, 0x0101044b }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_toId 1
int styleable AnimatedStateListDrawableTransition_android_fromId 2
int styleable AnimatedStateListDrawableTransition_android_reversible 3
int[] styleable Capability { 0x7f030025, 0x7f030029 }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f030001, 0x7f03001a }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable FontFamily { 0x7f03000c, 0x7f03000d, 0x7f03000e, 0x7f03000f, 0x7f030010, 0x7f030011, 0x7f030012, 0x7f030013 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFallbackQuery 2
int styleable FontFamily_fontProviderFetchStrategy 3
int styleable FontFamily_fontProviderFetchTimeout 4
int styleable FontFamily_fontProviderPackage 5
int styleable FontFamily_fontProviderQuery 6
int styleable FontFamily_fontProviderSystemFontFamily 7
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f03000b, 0x7f030014, 0x7f030015, 0x7f030016, 0x7f03002c }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable Fragment { 0x01010003, 0x010100d0, 0x010100d1 }
int styleable Fragment_android_name 0
int styleable Fragment_android_id 1
int styleable Fragment_android_tag 2
int[] styleable FragmentContainerView { 0x01010003, 0x010100d1 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable LoadingImageView { 0x7f030004, 0x7f030018, 0x7f030019 }
int styleable LoadingImageView_circleCrop 0
int styleable LoadingImageView_imageAspectRatio 1
int styleable LoadingImageView_imageAspectRatioAdjust 2
int[] styleable NavAction { 0x010100d0, 0x7f030008, 0x7f030009, 0x7f03000a, 0x7f03001b, 0x7f030020, 0x7f030021, 0x7f030022, 0x7f030023, 0x7f030024, 0x7f030026 }
int styleable NavAction_android_id 0
int styleable NavAction_destination 1
int styleable NavAction_enterAnim 2
int styleable NavAction_exitAnim 3
int styleable NavAction_launchSingleTop 4
int styleable NavAction_popEnterAnim 5
int styleable NavAction_popExitAnim 6
int styleable NavAction_popUpTo 7
int styleable NavAction_popUpToInclusive 8
int styleable NavAction_popUpToSaveState 9
int styleable NavAction_restoreState 10
int[] styleable NavArgument { 0x01010003, 0x010101ed, 0x7f030002, 0x7f03001f }
int styleable NavArgument_android_name 0
int styleable NavArgument_android_defaultValue 1
int styleable NavArgument_argType 2
int styleable NavArgument_nullable 3
int[] styleable NavDeepLink { 0x010104ee, 0x7f030000, 0x7f03001c, 0x7f03002d }
int styleable NavDeepLink_android_autoVerify 0
int styleable NavDeepLink_action 1
int styleable NavDeepLink_mimeType 2
int styleable NavDeepLink_uri 3
int[] styleable NavGraphNavigator { 0x7f03002a }
int styleable NavGraphNavigator_startDestination 0
int[] styleable NavHost { 0x7f03001d }
int styleable NavHost_navGraph 0
int[] styleable NavInclude { 0x7f030017 }
int styleable NavInclude_graph 0
int[] styleable Navigator { 0x01010001, 0x010100d0, 0x7f030027 }
int styleable Navigator_android_label 0
int styleable Navigator_android_id 1
int styleable Navigator_route 2
int[] styleable SignInButton { 0x7f030003, 0x7f030005, 0x7f030028 }
int styleable SignInButton_buttonSize 0
int styleable SignInButton_colorScheme 1
int styleable SignInButton_scopeUris 2
int[] styleable StateListDrawable { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable StateListDrawable_android_dither 0
int styleable StateListDrawable_android_visible 1
int styleable StateListDrawable_android_variablePadding 2
int styleable StateListDrawable_android_constantSize 3
int styleable StateListDrawable_android_enterFadeDuration 4
int styleable StateListDrawable_android_exitFadeDuration 5
int[] styleable StateListDrawableItem { 0x01010199 }
int styleable StateListDrawableItem_android_drawable 0
int xml backup_rules 0x7f0f0000
int xml data_extraction_rules 0x7f0f0001
int xml image_share_filepaths 0x7f0f0002
