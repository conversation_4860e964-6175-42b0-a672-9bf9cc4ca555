package com.trustmarket.trust.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import com.trustmarket.trust.data.models.Notification
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NotificationsScreen(
    onBackClick: () -> Unit
) {
    // Mock data - replace with real data from ViewModel
    val notifications = remember {
        listOf(
            Notification(
                id = "1",
                title = "رسالة جديدة",
                message = "أحمد محمد أرسل لك رسالة",
                type = "message",
                fromUserId = "user1",
                fromUserName = "أحمد محمد",
                fromUserAvatar = "",
                timestamp = System.currentTimeMillis() - 300000,
                isRead = false
            ),
            Notification(
                id = "2",
                title = "إعجاب برسالة",
                message = "فاطمة علي أعجبت برسالتك",
                type = "like",
                fromUserId = "user2",
                fromUserName = "فاطمة علي",
                fromUserAvatar = "",
                timestamp = System.currentTimeMillis() - 600000,
                isRead = false
            ),
            Notification(
                id = "3",
                title = "ذكر في رسالة",
                message = "تم ذكرك في الغرفة العامة",
                type = "mention",
                fromUserId = "user3",
                fromUserName = "سارة أحمد",
                fromUserAvatar = "",
                timestamp = System.currentTimeMillis() - 1800000,
                isRead = true
            ),
            Notification(
                id = "4",
                title = "مكافأة جديدة",
                message = "حصلت على 10 تذاكر من عجلة الحظ",
                type = "reward",
                fromUserId = "system",
                fromUserName = "النظام",
                fromUserAvatar = "",
                timestamp = System.currentTimeMillis() - 3600000,
                isRead = true
            )
        )
    }
    
    val unreadCount = notifications.count { !it.isRead }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "الإشعارات",
                        fontWeight = FontWeight.Bold
                    )
                    
                    if (unreadCount > 0) {
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Surface(
                            shape = CircleShape,
                            color = MaterialTheme.colorScheme.error
                        ) {
                            Text(
                                text = unreadCount.toString(),
                                color = MaterialTheme.colorScheme.onError,
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Bold,
                                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                            )
                        }
                    }
                }
            },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "العودة"
                    )
                }
            },
            actions = {
                if (unreadCount > 0) {
                    TextButton(
                        onClick = { /* Mark all as read */ }
                    ) {
                        Text(
                            text = "قراءة الكل",
                            color = MaterialTheme.colorScheme.onPrimary
                        )
                    }
                }
                
                IconButton(onClick = { /* Settings */ }) {
                    Icon(
                        imageVector = Icons.Default.Settings,
                        contentDescription = "إعدادات الإشعارات"
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = MaterialTheme.colorScheme.primary,
                titleContentColor = MaterialTheme.colorScheme.onPrimary,
                navigationIconContentColor = MaterialTheme.colorScheme.onPrimary,
                actionIconContentColor = MaterialTheme.colorScheme.onPrimary
            )
        )
        
        // Content
        if (notifications.isEmpty()) {
            // Empty state
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Notifications,
                    contentDescription = null,
                    modifier = Modifier.size(80.dp),
                    tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "لا توجد إشعارات",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
                
                Text(
                    text = "ستظهر الإشعارات الجديدة هنا",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                )
            }
        } else {
            // Notifications list
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(vertical = 8.dp)
            ) {
                items(notifications) { notification ->
                    NotificationCard(
                        notification = notification,
                        onClick = { /* Handle notification click */ }
                    )
                }
            }
        }
    }
}

@Composable
fun NotificationCard(
    notification: Notification,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 4.dp)
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = if (notification.isRead) {
                MaterialTheme.colorScheme.surface
            } else {
                MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.1f)
            }
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (notification.isRead) 1.dp else 2.dp
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.Top
        ) {
            // Notification icon
            Surface(
                modifier = Modifier.size(40.dp),
                shape = CircleShape,
                color = getNotificationIconColor(notification.type)
            ) {
                Icon(
                    imageVector = getNotificationIcon(notification.type),
                    contentDescription = null,
                    modifier = Modifier.padding(8.dp),
                    tint = Color.White
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // Notification content
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = notification.title,
                        fontSize = 16.sp,
                        fontWeight = if (notification.isRead) FontWeight.Medium else FontWeight.Bold,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.weight(1f)
                    )
                    
                    Text(
                        text = formatNotificationTime(notification.timestamp),
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                }
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = notification.message,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f),
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
                
                if (notification.fromUserName.isNotEmpty() && notification.fromUserName != "النظام") {
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    Text(
                        text = "من: ${notification.fromUserName}",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.primary,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
            
            // Unread indicator
            if (!notification.isRead) {
                Spacer(modifier = Modifier.width(8.dp))
                
                Surface(
                    modifier = Modifier.size(8.dp),
                    shape = CircleShape,
                    color = MaterialTheme.colorScheme.primary
                ) {}
            }
        }
    }
}

private fun getNotificationIcon(type: String) = when (type) {
    "message" -> Icons.Default.Message
    "mention" -> Icons.Default.AlternateEmail
    "like" -> Icons.Default.Favorite
    "reward" -> Icons.Default.CardGiftcard
    "system" -> Icons.Default.Info
    else -> Icons.Default.Notifications
}

private fun getNotificationIconColor(type: String) = when (type) {
    "message" -> Color(0xFF2196F3)
    "mention" -> Color(0xFF9C27B0)
    "like" -> Color(0xFFE91E63)
    "reward" -> Color(0xFFFF9800)
    "system" -> Color(0xFF607D8B)
    else -> Color(0xFF757575)
}

private fun formatNotificationTime(timestamp: Long): String {
    val now = System.currentTimeMillis()
    val diff = now - timestamp
    
    return when {
        diff < 60 * 1000 -> "الآن"
        diff < 60 * 60 * 1000 -> "${diff / (60 * 1000)} د"
        diff < 24 * 60 * 60 * 1000 -> "${diff / (60 * 60 * 1000)} س"
        diff < 7 * 24 * 60 * 60 * 1000 -> "${diff / (24 * 60 * 60 * 1000)} ي"
        else -> {
            val sdf = SimpleDateFormat("dd/MM", Locale.getDefault())
            sdf.format(Date(timestamp))
        }
    }
}
