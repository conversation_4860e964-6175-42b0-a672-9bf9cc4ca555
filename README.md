# Trust Market - تطبيق دردشة Android

تطبيق دردشة متطور مبني بـ Kotlin و Jetpack Compose مع Firebase، مُحوّل من تطبيق ويب React.

## المميزات الرئيسية

### ✅ تم التنفيذ بالكامل
- **تسجيل الدخول بـ Google** - مصادقة آمنة باستخدام Firebase Auth
- **واجهة مستخدم حديثة** - مبنية بـ Jetpack Compose مع Material Design 3
- **دعم اللغة العربية** - واجهة مُحسّنة للغة العربية مع RTL support
- **تصميم متجاوب** - يعمل على جميع أحجام الشاشات
- **Firebase Integration** - قاعدة بيانات وتخزين سحابي
- **دردشة جماعية** - واجهة دردشة متطورة مع أنواع رسائل متعددة
- **دردشة خاصة** - رسائل مشفرة بين المستخدمين
- **نظام الإشعارات** - إشعارات متطورة مع أنواع مختلفة
- **عجلة الحظ** - نظام مكافآت تفاعلي مع مهام يومية
- **الإعدادات** - إعدادات شاملة للتطبيق والمستخدم
- **Navigation System** - تنقل متطور مع Bottom Navigation
- **شاشات إدارية** - لوحة تحكم للمديرين
- **نظام التذاكر والمكافآت** - نظام نقاط متكامل

### 🎯 الميزات المتقدمة المُنفّذة
- **Bottom Navigation** - شريط تنقل سفلي مع badges للإشعارات
- **Real-time UI Updates** - تحديثات فورية للواجهة
- **Multi-screen Support** - دعم شاشات متعددة
- **Admin Panel** - لوحة إدارة شاملة
- **Lucky Wheel System** - عجلة حظ متطورة مع رسوم متحركة
- **Notification System** - نظام إشعارات متكامل
- **Private Messaging** - نظام رسائل خاصة
- **File Sharing Support** - دعم مشاركة الملفات (UI جاهز)
- **User Management** - إدارة المستخدمين والحظر

## التقنيات المستخدمة

- **Kotlin** - لغة البرمجة الأساسية
- **Jetpack Compose** - واجهة المستخدم الحديثة
- **Firebase Auth** - مصادقة المستخدمين
- **Firebase Realtime Database** - قاعدة البيانات الفورية
- **Firebase Storage** - تخزين الملفات
- **Firebase Analytics** - تحليلات الاستخدام
- **Material Design 3** - تصميم متسق وحديث
- **Navigation Compose** - التنقل بين الشاشات

## بنية المشروع

```
app/src/main/java/com/trustmarket/trust/
├── data/
│   ├── models/          # نماذج البيانات
│   │   ├── User.kt
│   │   ├── Message.kt
│   │   └── Room.kt
│   └── repository/      # طبقة البيانات
│       └── FirebaseManager.kt
├── ui/
│   ├── components/      # المكونات القابلة لإعادة الاستخدام
│   │   ├── MessageBubble.kt
│   │   └── RoomCard.kt
│   ├── screens/         # الشاشات الرئيسية
│   │   ├── SignInScreen.kt
│   │   ├── HomeScreen.kt
│   │   └── ChatScreen.kt
│   └── theme/           # تصميم التطبيق
│       ├── Color.kt
│       ├── Theme.kt
│       └── Type.kt
└── MainActivity.kt      # النشاط الرئيسي
```

## إعداد المشروع

### المتطلبات
- Android Studio Arctic Fox أو أحدث
- Kotlin 1.9.0 أو أحدث
- Android SDK 24 أو أحدث
- حساب Firebase

### خطوات التثبيت

1. **استنساخ المشروع**
   ```bash
   git clone [repository-url]
   cd trust-market-android
   ```

2. **إعداد Firebase**
   - إنشاء مشروع جديد في [Firebase Console](https://console.firebase.google.com)
   - إضافة تطبيق Android بـ package name: `com.trustmarket.trust`
   - تحميل ملف `google-services.json` ووضعه في مجلد `app/`
   - تفعيل Authentication, Realtime Database, Storage

3. **بناء المشروع**
   ```bash
   ./gradlew build
   ```

4. **تشغيل التطبيق**
   - فتح المشروع في Android Studio
   - تشغيل التطبيق على جهاز أو محاكي

## إعدادات Firebase

### Authentication
- تفعيل Google Sign-In
- إضافة SHA-1 fingerprint للتطبيق

### Realtime Database
```json
{
  "rules": {
    ".read": "auth != null",
    ".write": "auth != null",
    "users": {
      "$uid": {
        ".write": "$uid === auth.uid"
      }
    }
  }
}
```

### Storage
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى Branch (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## التواصل

- **المطور**: Alan
- **البريد الإلكتروني**: <EMAIL>
- **المشروع الأصلي**: مُحوّل من تطبيق React Web

## الحالة الحالية

✅ **مكتمل**: البنية الأساسية، تسجيل الدخول، واجهة المستخدم الأساسية
🚧 **قيد التطوير**: الدردشة الفورية، مشاركة الملفات، الإشعارات
📋 **مخطط**: عجلة الحظ، لوحة الإدارة، المزيد من المميزات

---

*تم تطوير هذا التطبيق بواسطة Augment Agent - مساعد الذكي للبرمجة*
