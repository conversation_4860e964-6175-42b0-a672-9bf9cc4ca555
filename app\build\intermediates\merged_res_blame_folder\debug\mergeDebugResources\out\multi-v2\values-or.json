{"logs": [{"outputFile": "com.trustmarket.trust.app-mergeDebugResources-70:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b44500860a60172f953a4f023e793d5e\\transformed\\foundation-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,87", "endOffsets": "135,223"}, "to": {"startLines": "109,110", "startColumns": "4,4", "startOffsets": "11783,11868", "endColumns": "84,87", "endOffsets": "11863,11951"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a4406fb22280fb302c67f992ef8133c\\transformed\\material3-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,294,407,526,620,720,837,980,1106,1257,1342,1447,1543,1638,1754,1884,1994,2137,2275,2406,2598,2724,2853,2988,3118,3215,3311,3428,3550,3655,3760,3863,4005,4155,4262,4371,4446,4550,4652,4763,4857,4948,5053,5133,5218,5319,5425,5518,5619,5706,5814,5913,6016,6140,6220,6323", "endColumns": "121,116,112,118,93,99,116,142,125,150,84,104,95,94,115,129,109,142,137,130,191,125,128,134,129,96,95,116,121,104,104,102,141,149,106,108,74,103,101,110,93,90,104,79,84,100,105,92,100,86,107,98,102,123,79,102,93", "endOffsets": "172,289,402,521,615,715,832,975,1101,1252,1337,1442,1538,1633,1749,1879,1989,2132,2270,2401,2593,2719,2848,2983,3113,3210,3306,3423,3545,3650,3755,3858,4000,4150,4257,4366,4441,4545,4647,4758,4852,4943,5048,5128,5213,5314,5420,5513,5614,5701,5809,5908,6011,6135,6215,6318,6412"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4418,4540,4657,4770,4889,4983,5083,5200,5343,5469,5620,5705,5810,5906,6001,6117,6247,6357,6500,6638,6769,6961,7087,7216,7351,7481,7578,7674,7791,7913,8018,8123,8226,8368,8518,8625,8734,8809,8913,9015,9126,9220,9311,9416,9496,9581,9682,9788,9881,9982,10069,10177,10276,10379,10503,10583,10686", "endColumns": "121,116,112,118,93,99,116,142,125,150,84,104,95,94,115,129,109,142,137,130,191,125,128,134,129,96,95,116,121,104,104,102,141,149,106,108,74,103,101,110,93,90,104,79,84,100,105,92,100,86,107,98,102,123,79,102,93", "endOffsets": "4535,4652,4765,4884,4978,5078,5195,5338,5464,5615,5700,5805,5901,5996,6112,6242,6352,6495,6633,6764,6956,7082,7211,7346,7476,7573,7669,7786,7908,8013,8118,8221,8363,8513,8620,8729,8804,8908,9010,9121,9215,9306,9411,9491,9576,9677,9783,9876,9977,10064,10172,10271,10374,10498,10578,10681,10775"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9f3b0e192fae8d81415bbd16b5199dc2\\transformed\\browser-1.4.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,270,383", "endColumns": "109,104,112,108", "endOffsets": "160,265,378,487"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3528,3916,4021,4134", "endColumns": "109,104,112,108", "endOffsets": "3633,4016,4129,4238"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\27e7e2f5ae47ff80d1406eee848a3aa7\\transformed\\ui-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,381,481,567,644,742,830,917,995,1077,1147,1222,1299,1375,1458,1525", "endColumns": "96,86,91,99,85,76,97,87,86,77,81,69,74,76,75,82,66,118", "endOffsets": "197,284,376,476,562,639,737,825,912,990,1072,1142,1217,1294,1370,1453,1520,1639"}, "to": {"startLines": "11,12,32,33,34,38,39,97,98,99,100,101,102,103,104,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1064,1161,3638,3730,3830,4243,4320,10780,10868,10955,11033,11115,11185,11260,11337,11514,11597,11664", "endColumns": "96,86,91,99,85,76,97,87,86,77,81,69,74,76,75,82,66,118", "endOffsets": "1156,1243,3725,3825,3911,4315,4413,10863,10950,11028,11110,11180,11255,11332,11408,11592,11659,11778"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e535ebbc8274cd26275a634ce7617c3d\\transformed\\core-1.16.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "4,5,6,7,8,9,10,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "329,432,534,637,742,843,945,11413", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "427,529,632,737,838,940,1059,11509"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9cef95d772ca94c574c1d7981123fcc6\\transformed\\play-services-base-18.5.0\\res\\values-or\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,457,585,698,849,980,1090,1196,1359,1468,1625,1754,1900,2053,2114,2182", "endColumns": "106,156,127,112,150,130,109,105,162,108,156,128,145,152,60,67,82", "endOffsets": "299,456,584,697,848,979,1089,1195,1358,1467,1624,1753,1899,2052,2113,2181,2264"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1248,1359,1520,1652,1769,1924,2059,2173,2423,2590,2703,2864,2997,3147,3304,3369,3441", "endColumns": "110,160,131,116,154,134,113,109,166,112,160,132,149,156,64,71,86", "endOffsets": "1354,1515,1647,1764,1919,2054,2168,2278,2585,2698,2859,2992,3142,3299,3364,3436,3523"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\34a6171889a3f0a2e7cbf13a2f1c0e80\\transformed\\credentials-1.2.0-rc01\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,111", "endOffsets": "162,274"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,217", "endColumns": "111,111", "endOffsets": "212,324"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\63ccc495eef38375f13c6ff523cfbb5a\\transformed\\play-services-basement-18.5.0\\res\\values-or\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2283", "endColumns": "139", "endOffsets": "2418"}}]}]}