package com.trustmarket.trust.data.models

import com.google.firebase.database.PropertyName

data class LuckyWheelReward(
    @PropertyName("id")
    val id: String = "",
    
    @PropertyName("name")
    val name: String = "",
    
    @PropertyName("description")
    val description: String = "",
    
    @PropertyName("type")
    val type: String = "tickets", // tickets, badge, special
    
    @PropertyName("value")
    val value: Int = 0,
    
    @PropertyName("color")
    val color: String = "#FF6B6B",
    
    @PropertyName("probability")
    val probability: Double = 0.1,
    
    @PropertyName("isActive")
    val isActive: Boolean = true
) {
    constructor() : this("", "", "", "tickets", 0, "#FF6B6B", 0.1, true)
}

data class UserTickets(
    @PropertyName("userId")
    val userId: String = "",
    
    @PropertyName("tickets")
    val tickets: Int = 0,
    
    @PropertyName("totalEarned")
    val totalEarned: Int = 0,
    
    @PropertyName("totalSpent")
    val totalSpent: Int = 0,
    
    @PropertyName("lastUpdated")
    val lastUpdated: Long = 0L,
    
    @PropertyName("dailyTasksCompleted")
    val dailyTasksCompleted: Map<String, Boolean> = emptyMap(),
    
    @PropertyName("lastDailyReset")
    val lastDailyReset: Long = 0L
) {
    constructor() : this("", 0, 0, 0, 0L, emptyMap(), 0L)
}

data class WheelSpin(
    @PropertyName("id")
    val id: String = "",
    
    @PropertyName("userId")
    val userId: String = "",
    
    @PropertyName("userName")
    val userName: String = "",
    
    @PropertyName("rewardId")
    val rewardId: String = "",
    
    @PropertyName("rewardName")
    val rewardName: String = "",
    
    @PropertyName("rewardValue")
    val rewardValue: Int = 0,
    
    @PropertyName("timestamp")
    val timestamp: Long = 0L
) {
    constructor() : this("", "", "", "", "", 0, 0L)
}
