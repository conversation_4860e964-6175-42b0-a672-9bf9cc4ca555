package com.trustmarket.trust.data.models

import com.google.firebase.database.PropertyName

data class User(
    @PropertyName("uid")
    val uid: String = "",
    
    @PropertyName("name")
    val name: String = "",
    
    @PropertyName("email")
    val email: String = "",
    
    @PropertyName("avatar")
    val avatar: String = "",
    
    @PropertyName("isOnline")
    val isOnline: Boolean = false,
    
    @PropertyName("lastOnline")
    val lastOnline: Long = 0L,
    
    @PropertyName("createdAt")
    val createdAt: Long = 0L,
    
    @PropertyName("isAdmin")
    val isAdmin: Boolean = false,
    
    @PropertyName("isBanned")
    val isBanned: Boolean = false,
    
    @PropertyName("banReason")
    val banReason: String = "",
    
    @PropertyName("language")
    val language: String = "ar"
) {
    // Constructor بدون معاملات مطلوب لـ Firebase
    constructor() : this("", "", "", "", false, 0L, 0L, false, false, "", "ar")
}
