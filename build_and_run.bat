@echo off
echo ========================================
echo Trust Market Android App - Build Script
echo ========================================
echo.

echo [1/4] Cleaning previous builds...
call gradlew clean
if %errorlevel% neq 0 (
    echo ERROR: Clean failed!
    pause
    exit /b 1
)

echo.
echo [2/4] Building project...
call gradlew build
if %errorlevel% neq 0 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo.
echo [3/4] Installing debug APK...
call gradlew installDebug
if %errorlevel% neq 0 (
    echo ERROR: Install failed!
    echo Make sure you have a device connected or emulator running
    pause
    exit /b 1
)

echo.
echo [4/4] Starting app on device...
adb shell am start -n com.trustmarket.trust/.MainActivity
if %errorlevel% neq 0 (
    echo WARNING: Could not start app automatically
    echo Please start the app manually from your device
)

echo.
echo ========================================
echo Build and deployment completed successfully!
echo App package: com.trustmarket.trust
echo ========================================
pause
