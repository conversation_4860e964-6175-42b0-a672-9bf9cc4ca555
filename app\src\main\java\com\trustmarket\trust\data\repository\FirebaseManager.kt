package com.trustmarket.trust.data.repository

import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.GoogleAuthProvider
import com.google.firebase.database.*
import com.google.firebase.storage.FirebaseStorage
import com.trustmarket.trust.data.models.User
import com.trustmarket.trust.data.models.Message
import com.trustmarket.trust.data.models.Room
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FirebaseManager @Inject constructor() {
    
    private val auth = FirebaseAuth.getInstance()
    private val database = FirebaseDatabase.getInstance()
    private val storage = FirebaseStorage.getInstance()
    
    // Authentication
    suspend fun signInWithGoogle(idToken: String): Result<User> {
        return try {
            val credential = GoogleAuthProvider.getCredential(idToken, null)
            val result = auth.signInWithCredential(credential).await()
            val firebaseUser = result.user ?: throw Exception("User is null")
            
            val user = User(
                uid = firebaseUser.uid,
                name = firebaseUser.displayName ?: "",
                email = firebaseUser.email ?: "",
                avatar = firebaseUser.photoUrl?.toString() ?: "",
                isOnline = true,
                lastOnline = System.currentTimeMillis(),
                createdAt = System.currentTimeMillis(),
                isAdmin = firebaseUser.email == "<EMAIL>"
            )
            
            // Save user to database
            saveUser(user)
            Result.success(user)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun signOut() {
        try {
            getCurrentUser()?.let { user ->
                updateUserOnlineStatus(user.uid, false)
            }
            auth.signOut()
        } catch (e: Exception) {
            // Handle error
        }
    }
    
    fun getCurrentUser(): User? {
        val firebaseUser = auth.currentUser ?: return null
        return User(
            uid = firebaseUser.uid,
            name = firebaseUser.displayName ?: "",
            email = firebaseUser.email ?: "",
            avatar = firebaseUser.photoUrl?.toString() ?: ""
        )
    }
    
    // User Management
    suspend fun saveUser(user: User) {
        database.reference.child("users").child(user.uid).setValue(user).await()
    }
    
    suspend fun updateUserOnlineStatus(userId: String, isOnline: Boolean) {
        val updates = mapOf(
            "isOnline" to isOnline,
            "lastOnline" to System.currentTimeMillis()
        )
        database.reference.child("users").child(userId).updateChildren(updates).await()
    }
    
    fun getUsersFlow(): Flow<List<User>> = callbackFlow {
        val listener = object : ValueEventListener {
            override fun onDataChange(snapshot: DataSnapshot) {
                val users = snapshot.children.mapNotNull { 
                    it.getValue(User::class.java) 
                }
                trySend(users)
            }
            
            override fun onCancelled(error: DatabaseError) {
                close(error.toException())
            }
        }
        
        val ref = database.reference.child("users")
        ref.addValueEventListener(listener)
        
        awaitClose { ref.removeEventListener(listener) }
    }
    
    // Room Management
    suspend fun createRoom(room: Room): Result<String> {
        return try {
            val roomRef = database.reference.child("rooms").push()
            val roomId = roomRef.key ?: throw Exception("Failed to generate room ID")
            val roomWithId = room.copy(id = roomId)
            roomRef.setValue(roomWithId).await()
            Result.success(roomId)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    fun getRoomsFlow(): Flow<List<Room>> = callbackFlow {
        val listener = object : ValueEventListener {
            override fun onDataChange(snapshot: DataSnapshot) {
                val rooms = snapshot.children.mapNotNull { 
                    it.getValue(Room::class.java) 
                }.filter { !it.isDeleted }
                trySend(rooms)
            }
            
            override fun onCancelled(error: DatabaseError) {
                close(error.toException())
            }
        }
        
        val ref = database.reference.child("rooms")
        ref.addValueEventListener(listener)
        
        awaitClose { ref.removeEventListener(listener) }
    }
    
    // Message Management
    suspend fun sendMessage(message: Message): Result<String> {
        return try {
            val messageRef = if (message.isPrivate) {
                database.reference.child("privateMessages").push()
            } else {
                database.reference.child("messages").child(message.roomId).push()
            }
            
            val messageId = messageRef.key ?: throw Exception("Failed to generate message ID")
            val messageWithId = message.copy(id = messageId)
            messageRef.setValue(messageWithId).await()
            Result.success(messageId)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    fun getMessagesFlow(roomId: String): Flow<List<Message>> = callbackFlow {
        val listener = object : ValueEventListener {
            override fun onDataChange(snapshot: DataSnapshot) {
                val messages = snapshot.children.mapNotNull { 
                    it.getValue(Message::class.java) 
                }.filter { !it.isDeleted }
                .sortedBy { it.timestamp }
                trySend(messages)
            }
            
            override fun onCancelled(error: DatabaseError) {
                close(error.toException())
            }
        }
        
        val ref = database.reference.child("messages").child(roomId)
        ref.addValueEventListener(listener)
        
        awaitClose { ref.removeEventListener(listener) }
    }
}
