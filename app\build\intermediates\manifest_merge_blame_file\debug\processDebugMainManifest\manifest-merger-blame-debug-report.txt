1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.trustmarket.trust"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Internet permission for Firebase and WebView -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.WAKE_LOCK" />
14-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:25:5-68
14-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:25:22-65
15    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
15-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
15-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
16    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
16-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2269f6a490db479487e1f60b8c759e15\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
16-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2269f6a490db479487e1f60b8c759e15\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:25:22-76
17    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
17-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2269f6a490db479487e1f60b8c759e15\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
17-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2269f6a490db479487e1f60b8c759e15\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
18    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
18-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2269f6a490db479487e1f60b8c759e15\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
18-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2269f6a490db479487e1f60b8c759e15\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
19    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
19-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\290fe9217f4b4b879ffe8d2a89d2ccc6\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
19-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\290fe9217f4b4b879ffe8d2a89d2ccc6\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
20
21    <permission
21-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e535ebbc8274cd26275a634ce7617c3d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
22        android:name="com.trustmarket.trust.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
22-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e535ebbc8274cd26275a634ce7617c3d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
23        android:protectionLevel="signature" />
23-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e535ebbc8274cd26275a634ce7617c3d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
24
25    <uses-permission android:name="com.trustmarket.trust.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
25-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e535ebbc8274cd26275a634ce7617c3d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
25-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e535ebbc8274cd26275a634ce7617c3d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
26
27    <application
27-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:9:5-30:19
28        android:allowBackup="true"
28-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:10:9-35
29        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
29-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e535ebbc8274cd26275a634ce7617c3d\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
30        android:dataExtractionRules="@xml/data_extraction_rules"
30-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:11:9-65
31        android:debuggable="true"
32        android:extractNativeLibs="false"
33        android:fullBackupContent="@xml/backup_rules"
33-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:12:9-54
34        android:icon="@mipmap/ic_launcher"
34-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:13:9-43
35        android:label="@string/app_name"
35-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:14:9-41
36        android:roundIcon="@mipmap/ic_launcher_round"
36-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:15:9-54
37        android:supportsRtl="true"
37-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:16:9-35
38        android:theme="@style/Theme.Trust" >
38-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:17:9-43
39        <activity
39-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:19:9-29:20
40            android:name="com.trustmarket.trust.MainActivity"
40-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:20:13-41
41            android:exported="true"
41-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:21:13-36
42            android:label="@string/app_name"
42-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:22:13-45
43            android:theme="@style/Theme.Trust" >
43-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:23:13-47
44            <intent-filter>
44-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:24:13-28:29
45                <action android:name="android.intent.action.MAIN" />
45-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:25:17-69
45-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:25:25-66
46
47                <category android:name="android.intent.category.LAUNCHER" />
47-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:27:17-77
47-->C:\Users\<USER>\Desktop\trust\app\src\main\AndroidManifest.xml:27:27-74
48            </intent-filter>
49        </activity>
50        <activity
50-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
51            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
51-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
52            android:excludeFromRecents="true"
52-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
53            android:exported="true"
53-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
54            android:launchMode="singleTask"
54-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
55            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
55-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
56            <intent-filter>
56-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
57                <action android:name="android.intent.action.VIEW" />
57-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
57-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
58
59                <category android:name="android.intent.category.DEFAULT" />
59-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
59-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
60                <category android:name="android.intent.category.BROWSABLE" />
60-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
60-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
61
62                <data
62-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
63                    android:host="firebase.auth"
63-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
64                    android:path="/"
64-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
65                    android:scheme="genericidp" />
65-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
66            </intent-filter>
67        </activity>
68        <activity
68-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
69            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
69-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
70            android:excludeFromRecents="true"
70-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
71            android:exported="true"
71-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
72            android:launchMode="singleTask"
72-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
73            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
73-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
74            <intent-filter>
74-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
75                <action android:name="android.intent.action.VIEW" />
75-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
75-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
76
77                <category android:name="android.intent.category.DEFAULT" />
77-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
77-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
78                <category android:name="android.intent.category.BROWSABLE" />
78-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
78-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
79
80                <data
80-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:41:17-44:51
81                    android:host="firebase.auth"
81-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:42:21-49
82                    android:path="/"
82-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:43:21-37
83                    android:scheme="recaptcha" />
83-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:44:21-48
84            </intent-filter>
85        </activity>
86
87        <service
87-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
88            android:name="com.google.firebase.components.ComponentDiscoveryService"
88-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:67:13-84
89            android:directBootAware="true"
89-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
90            android:exported="false" >
90-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
91            <meta-data
91-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
92                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
92-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
93                android:value="com.google.firebase.components.ComponentRegistrar" />
93-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4094a2d30f21d338746feefffbbee192\transformed\firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
94            <meta-data
94-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afec609340cf35cbdd99288228d31f83\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
95                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
95-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afec609340cf35cbdd99288228d31f83\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
96                android:value="com.google.firebase.components.ComponentRegistrar" />
96-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afec609340cf35cbdd99288228d31f83\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
97            <meta-data
97-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afec609340cf35cbdd99288228d31f83\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
98                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
98-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afec609340cf35cbdd99288228d31f83\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
99                android:value="com.google.firebase.components.ComponentRegistrar" />
99-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afec609340cf35cbdd99288228d31f83\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
100            <meta-data
100-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2269f6a490db479487e1f60b8c759e15\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
101                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
101-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2269f6a490db479487e1f60b8c759e15\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
102                android:value="com.google.firebase.components.ComponentRegistrar" />
102-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2269f6a490db479487e1f60b8c759e15\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
103            <meta-data
103-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a255781ae5900fa88d835456e104a7b8\transformed\firebase-storage-21.0.2\AndroidManifest.xml:30:13-32:85
104                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
104-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a255781ae5900fa88d835456e104a7b8\transformed\firebase-storage-21.0.2\AndroidManifest.xml:31:17-118
105                android:value="com.google.firebase.components.ComponentRegistrar" />
105-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a255781ae5900fa88d835456e104a7b8\transformed\firebase-storage-21.0.2\AndroidManifest.xml:32:17-82
106            <meta-data
106-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a255781ae5900fa88d835456e104a7b8\transformed\firebase-storage-21.0.2\AndroidManifest.xml:33:13-35:85
107                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
107-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a255781ae5900fa88d835456e104a7b8\transformed\firebase-storage-21.0.2\AndroidManifest.xml:34:17-107
108                android:value="com.google.firebase.components.ComponentRegistrar" />
108-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a255781ae5900fa88d835456e104a7b8\transformed\firebase-storage-21.0.2\AndroidManifest.xml:35:17-82
109            <meta-data
109-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
110                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
110-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
111                android:value="com.google.firebase.components.ComponentRegistrar" />
111-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
112            <meta-data
112-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
113                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
113-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
114                android:value="com.google.firebase.components.ComponentRegistrar" />
114-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
115            <meta-data
115-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
116                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
116-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
117                android:value="com.google.firebase.components.ComponentRegistrar" />
117-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
118            <meta-data
118-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
119                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
119-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
120                android:value="com.google.firebase.components.ComponentRegistrar" />
120-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
121            <meta-data
121-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8a49351139c0acd4d23330a6344893\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
122                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
122-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8a49351139c0acd4d23330a6344893\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
123                android:value="com.google.firebase.components.ComponentRegistrar" />
123-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8a49351139c0acd4d23330a6344893\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
124            <meta-data
124-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
125                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
125-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
126                android:value="com.google.firebase.components.ComponentRegistrar" />
126-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
127        </service>
128
129        <receiver
129-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
130            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
130-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
131            android:enabled="true"
131-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
132            android:exported="false" >
132-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
133        </receiver>
134
135        <service
135-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
136            android:name="com.google.android.gms.measurement.AppMeasurementService"
136-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
137            android:enabled="true"
137-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
138            android:exported="false" />
138-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
139        <service
139-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
140            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
140-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
141            android:enabled="true"
141-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
142            android:exported="false"
142-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
143            android:permission="android.permission.BIND_JOB_SERVICE" />
143-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\331fb39f64dec4b5fe9c1469dc37ed22\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
144        <service
144-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
145            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
145-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
146            android:enabled="true"
146-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
147            android:exported="false" >
147-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
148            <meta-data
148-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
149                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
149-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
150                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
150-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
151        </service>
152
153        <activity
153-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
154            android:name="androidx.credentials.playservices.HiddenActivity"
154-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
155            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
155-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
156            android:enabled="true"
156-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
157            android:exported="false"
157-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
158            android:fitsSystemWindows="true"
158-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
159            android:theme="@style/Theme.Hidden" >
159-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518565318f569a8f40477eeed556b992\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
160        </activity>
161        <activity
161-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60a93836d30073ec518084db27eb686c\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
162            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
162-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60a93836d30073ec518084db27eb686c\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
163            android:excludeFromRecents="true"
163-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60a93836d30073ec518084db27eb686c\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
164            android:exported="false"
164-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60a93836d30073ec518084db27eb686c\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
165            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
165-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60a93836d30073ec518084db27eb686c\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
166        <!--
167            Service handling Google Sign-In user revocation. For apps that do not integrate with
168            Google Sign-In, this service will never be started.
169        -->
170        <service
170-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60a93836d30073ec518084db27eb686c\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
171            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
171-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60a93836d30073ec518084db27eb686c\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
172            android:exported="true"
172-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60a93836d30073ec518084db27eb686c\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
173            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
173-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60a93836d30073ec518084db27eb686c\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
174            android:visibleToInstantApps="true" />
174-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60a93836d30073ec518084db27eb686c\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
175
176        <provider
176-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
177            android:name="com.google.firebase.provider.FirebaseInitProvider"
177-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
178            android:authorities="com.trustmarket.trust.firebaseinitprovider"
178-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
179            android:directBootAware="true"
179-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
180            android:exported="false"
180-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
181            android:initOrder="100" />
181-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
182
183        <activity
183-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6c35db505466d3a5c4efc405a67be1c8\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
184            android:name="androidx.activity.ComponentActivity"
184-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6c35db505466d3a5c4efc405a67be1c8\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
185            android:exported="true" />
185-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6c35db505466d3a5c4efc405a67be1c8\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
186        <activity
186-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d4c9730f1bd1e7807d584b5aa7b806b\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
187            android:name="androidx.compose.ui.tooling.PreviewActivity"
187-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d4c9730f1bd1e7807d584b5aa7b806b\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
188            android:exported="true" />
188-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d4c9730f1bd1e7807d584b5aa7b806b\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
189        <activity
189-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
190            android:name="com.google.android.gms.common.api.GoogleApiActivity"
190-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
191            android:exported="false"
191-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
192            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
192-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
193
194        <provider
194-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
195            android:name="androidx.startup.InitializationProvider"
195-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
196            android:authorities="com.trustmarket.trust.androidx-startup"
196-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
197            android:exported="false" >
197-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
198            <meta-data
198-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
199                android:name="androidx.emoji2.text.EmojiCompatInitializer"
199-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
200                android:value="androidx.startup" />
200-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
201            <meta-data
201-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b24052f1db77d6bbe4309c4f2fc3cb7\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
202                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
202-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b24052f1db77d6bbe4309c4f2fc3cb7\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
203                android:value="androidx.startup" />
203-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b24052f1db77d6bbe4309c4f2fc3cb7\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
204            <meta-data
204-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
205                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
205-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
206                android:value="androidx.startup" />
206-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
207        </provider>
208
209        <uses-library
209-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aec5efcf300c1ea9f0193c5ce10a1517\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
210            android:name="android.ext.adservices"
210-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aec5efcf300c1ea9f0193c5ce10a1517\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
211            android:required="false" />
211-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aec5efcf300c1ea9f0193c5ce10a1517\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
212
213        <meta-data
213-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63ccc495eef38375f13c6ff523cfbb5a\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
214            android:name="com.google.android.gms.version"
214-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63ccc495eef38375f13c6ff523cfbb5a\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
215            android:value="@integer/google_play_services_version" />
215-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63ccc495eef38375f13c6ff523cfbb5a\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
216
217        <receiver
217-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
218            android:name="androidx.profileinstaller.ProfileInstallReceiver"
218-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
219            android:directBootAware="false"
219-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
220            android:enabled="true"
220-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
221            android:exported="true"
221-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
222            android:permission="android.permission.DUMP" >
222-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
223            <intent-filter>
223-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
224                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
224-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
224-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
225            </intent-filter>
226            <intent-filter>
226-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
227                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
227-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
227-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
228            </intent-filter>
229            <intent-filter>
229-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
230                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
230-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
230-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
231            </intent-filter>
232            <intent-filter>
232-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
233                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
233-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
233-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94ad64668a88e176fd1b7b5afefb3866\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
234            </intent-filter>
235        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
236        <activity
236-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
237            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
237-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
238            android:exported="false"
238-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
239            android:stateNotNeeded="true"
239-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
240            android:theme="@style/Theme.PlayCore.Transparent" />
240-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
241    </application>
242
243</manifest>
