package com.trustmarket.trust.navigation

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.trustmarket.trust.ui.screens.*
import com.trustmarket.trust.ui.components.EnhancedBottomNavigationBar

sealed class Screen(val route: String) {
    object SignIn : Screen("signin")
    object Home : Screen("home")
    object Chat : Screen("chat/{roomId}") {
        fun createRoute(roomId: String) = "chat/$roomId"
    }
    object PrivateChats : Screen("private_chats")
    object PrivateChat : Screen("private_chat/{userId}") {
        fun createRoute(userId: String) = "private_chat/$userId"
    }
    object Settings : Screen("settings")
    object Notifications : Screen("notifications")
    object LuckyWheel : Screen("lucky_wheel")
    object LuckyWheelAdmin : Screen("lucky_wheel_admin")
    object RewardCodeChecker : Screen("reward_code_checker")
    object DeletedRooms : Screen("deleted_rooms")
    object BannedUsers : Screen("banned_users")
    object Records : Screen("records")
}

@Composable
fun TrustNavigation(
    navController: NavHostController = rememberNavController(),
    isSignedIn: Boolean,
    startDestination: String = if (isSignedIn) Screen.Home.route else Screen.SignIn.route
) {
    if (isSignedIn) {
        Scaffold(
            bottomBar = {
                EnhancedBottomNavigationBar(
                    navController = navController,
                    unreadChatsCount = 3, // Mock data
                    unreadNotificationsCount = 5 // Mock data
                )
            }
        ) { paddingValues ->
            NavHost(
                navController = navController,
                startDestination = startDestination,
                modifier = Modifier.padding(paddingValues)
            ) {
                MainNavigationGraph(navController)
            }
        }
    } else {
        NavHost(
            navController = navController,
            startDestination = startDestination
        ) {
            AuthNavigationGraph(navController)
        }
    }
}

private fun NavGraphBuilder.AuthNavigationGraph(navController: NavHostController) {
    // Sign In Screen
    composable(Screen.SignIn.route) {
        SignInScreen(
            onGoogleSignIn = {
                // Handle Google Sign In
                navController.navigate(Screen.Home.route) {
                    popUpTo(Screen.SignIn.route) { inclusive = true }
                }
            }
        )
    }
}

private fun NavGraphBuilder.MainNavigationGraph(navController: NavHostController) {
        
        // Home Screen
        composable(Screen.Home.route) {
            HomeScreen(
                onRoomClick = { room ->
                    navController.navigate(Screen.Chat.createRoute(room.id))
                },
                onPrivateChats = {
                    navController.navigate(Screen.PrivateChats.route)
                },
                onSettings = {
                    navController.navigate(Screen.Settings.route)
                },
                onNotifications = {
                    navController.navigate(Screen.Notifications.route)
                },
                onLuckyWheel = {
                    navController.navigate(Screen.LuckyWheel.route)
                },
                onSignOut = {
                    navController.navigate(Screen.SignIn.route) {
                        popUpTo(Screen.Home.route) { inclusive = true }
                    }
                }
            )
        }
        
        // Chat Screen
        composable(Screen.Chat.route) { backStackEntry ->
            val roomId = backStackEntry.arguments?.getString("roomId") ?: ""
            ChatScreen(
                roomId = roomId,
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }
        
        // Private Chats Screen
        composable(Screen.PrivateChats.route) {
            PrivateChatsScreen(
                onBackClick = {
                    navController.popBackStack()
                },
                onChatClick = { userId ->
                    navController.navigate(Screen.PrivateChat.createRoute(userId))
                }
            )
        }
        
        // Private Chat Screen
        composable(Screen.PrivateChat.route) { backStackEntry ->
            val userId = backStackEntry.arguments?.getString("userId") ?: ""
            PrivateChatScreen(
                userId = userId,
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }
        
        // Settings Screen
        composable(Screen.Settings.route) {
            SettingsScreen(
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }
        
        // Notifications Screen
        composable(Screen.Notifications.route) {
            NotificationsScreen(
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }
        
        // Lucky Wheel Screen
        composable(Screen.LuckyWheel.route) {
            LuckyWheelScreen(
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }
        
        // Lucky Wheel Admin Screen
        composable(Screen.LuckyWheelAdmin.route) {
            LuckyWheelAdminScreen(
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }
        
        // Reward Code Checker Screen
        composable(Screen.RewardCodeChecker.route) {
            RewardCodeCheckerScreen(
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }
        
        // Deleted Rooms Screen (Admin)
        composable(Screen.DeletedRooms.route) {
            DeletedRoomsScreen(
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }
        
        // Banned Users Screen (Admin)
        composable(Screen.BannedUsers.route) {
            BannedUsersScreen(
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }
        
        // Records Screen (Admin)
        composable(Screen.Records.route) {
            RecordsScreen(
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }
    }
}
