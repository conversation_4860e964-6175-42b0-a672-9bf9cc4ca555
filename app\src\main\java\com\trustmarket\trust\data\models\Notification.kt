package com.trustmarket.trust.data.models

import com.google.firebase.database.PropertyName

data class Notification(
    @PropertyName("id")
    val id: String = "",
    
    @PropertyName("title")
    val title: String = "",
    
    @PropertyName("message")
    val message: String = "",
    
    @PropertyName("type")
    val type: String = "message", // message, mention, like, system, reward
    
    @PropertyName("fromUserId")
    val fromUserId: String = "",
    
    @PropertyName("fromUserName")
    val fromUserName: String = "",
    
    @PropertyName("fromUserAvatar")
    val fromUserAvatar: String = "",
    
    @PropertyName("toUserId")
    val toUserId: String = "",
    
    @PropertyName("roomId")
    val roomId: String = "",
    
    @PropertyName("messageId")
    val messageId: String = "",
    
    @PropertyName("timestamp")
    val timestamp: Long = 0L,
    
    @PropertyName("isRead")
    val isRead: Boolean = false,
    
    @PropertyName("data")
    val data: Map<String, String> = emptyMap()
) {
    constructor() : this("", "", "", "message", "", "", "", "", "", "", 0L, false, emptyMap())
}
