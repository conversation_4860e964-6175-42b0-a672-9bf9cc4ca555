# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.0.21"
  }
  digests {
    sha256: "\363\034\305?\020Z~H\300\223h;\275T7V\035\0223\222\005\023wKG\b\005d\033\355\274\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.9.10"
  }
  digests {
    sha256: "\244\307M\224\326L\341\253\3457`\376\003\211\335\224\037o\305X\320\332\263^G\300\205\241\036\310\017("
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.10"
  }
  digests {
    sha256: "\254ca\277\232\321\3558,!\003\331q,G\315\354\026b2\264\220>\325\226\350\207k\006\201\311\267"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.0.21"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.16.0"
  }
  digests {
    sha256: "\027f\333\330/d\241-\3155\236\313o\025\363\3175\333Mf\322)a\247\305\033/\374dh1L"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.9.1"
  }
  digests {
    sha256: "\03649\027\353\362{\251o\344\334R\261\312\327\3752\2678\373\3065[\266\315[;0]r\022\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.16.0"
  }
  digests {
    sha256: "k\360=9\333\343tJ\314\342\'\323\266\2277L6%\252\341\002_\276\310\255\237\327\275X\274\3441"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.4"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.4"
  }
  digests {
    sha256: "S\232CB\215\370\2437b/\267\201@\037_\f\334\325-\2136\025\360\bZ\365\034\220\000/\3633"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.4.4"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-viewtree"
    version: "1.0.0"
  }
  digests {
    sha256: "\334\033g\215X\353\317+\372\025\207\276h\377\202f\224\316=\"\022Q\271\3570\324\324\263b\227\346\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\324\":\346\250Dvw&}\377\"\206;mi\210\312h\264\322|\246\327K\201\004k\004yBW"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-jvm"
    version: "2.9.0"
  }
  digests {
    sha256: "7\330\233!\001\360t\254l&\t\027\332\273\030V\ad^\342\000\2520\030\307\305\275\347\016\334\361\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-play-services"
    version: "1.7.3"
  }
  digests {
    sha256: "d\326\352\032M\025\242\300\225\r\251\246\037I\352|Q&\216\347L;\035\335\304c\346\225TD\033$"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.2.0"
  }
  digests {
    sha256: "\177*\252\217P h\352\365CV\312\222\256\300Bq\326\347\304\026\305,E\300\3224@\374\275\026T"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.5.0"
  }
  digests {
    sha256: "\243\337n\373)\276\262\377x\373|\336q*\307s\275l\334\337\311\203<\220\030\225\200O`\234\231^"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.5.1"
  }
  digests {
    sha256: "\202\260G\200\355\242\033\n^\352\002GT\025\210z\034\255\3736\2137\022!\256\264\a\003\300g\3638"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.10.1"
  }
  digests {
    sha256: "\266+R\214\221}\351\276I~\266\370\2100\031| \351\322\022g\303\221la4\222\345\356\203}M"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.9.0"
  }
  digests {
    sha256: "\312\264\004\260]_\256\322\t\020J*\357\024\300\313\263\026\253\237\253\344\250\3607\260C\345\322\36445"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\372\235u\021\250\376\r^\334S\t\227\006\326O7b\035\317\353\237,Usi\355\203d_\310z#"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.9.0"
  }
  digests {
    sha256: "I}\330M\351\362\375F2O%\371\243\257\003-:#\320\207\350z\365\025\304\261\2245\366\326\246Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.9.0"
  }
  digests {
    sha256: "F8-\337\2724\215\271xF\317\250\035g\327#\004\334\025k\271b$5\213B\237\347H\274j\264"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.9.0"
  }
  digests {
    sha256: "d\371kR\307\b\225\200\1770 \342\350\372\021\363\354-\251\301W\366*\256!\242\311\323\264\204\247G"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jspecify"
    artifactId: "jspecify"
    version: "1.0.0"
  }
  digests {
    sha256: "\037\255nk\347Uw\201\344\3237)\324\232\341\315\310\375\332o\344w\273\f\306\214\343Q\352\375\373\253"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx-android"
    version: "2.9.0"
  }
  digests {
    sha256: "3?\242\316m\267\n\303r\267\221\337\353\251\234\002\300\331{\032\213J\264\250MGbFm\022\262\a"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-compose"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-compose-android"
    version: "2.9.0"
  }
  digests {
    sha256: "}\177\374\f\307l\225\201A<\250\251\256?ginI\354\250\v6C\313\255\222B\207\221\037N\200"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\fNcf\243\f\361\364d`\0324\236\016,\336\333p\337Sr\362\332\205\227\346\020\207\251\020\250\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable-android"
    version: "1.7.8"
  }
  digests {
    sha256: "b\202\364\256s\224\215T\320H\034Z|\317_\'\017\331\356tq\231\361Nc\023\363\314\3209\266\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\325O\261z\207\344\004\360`,I\365\312H\377\n\356@UE\034\232Z<\217\024B\016\aS\321\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.10.1"
  }
  digests {
    sha256: "\363\226\365\215\275w,\006[\2076\226\267J=M\buT\263vb\200\346;I\262]\273S\253\026"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.9.0"
  }
  digests {
    sha256: "\257B\375\204\364\345+D\334\253\257\236\317\224\022\005S\222\233\301\240Y\276\262$\n\236\301\333\356\201O"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\3336x8qnU\206h#\306\336\355b\374\354#\374\344\207\277*\217\245e\243o\024\232\263\321\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.3.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-android"
    version: "1.3.0"
  }
  digests {
    sha256: "!\vi\t\273\325\025\333\364\025\220\\\273\"\017\312\223\250\177\245F\203\217u@w\353\310\340H\205\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\360\255\336E\206ADGS\205\317J\247\340\267\376\262\177a\374\371G&e\355\230\314\227\033\006\261\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-json"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-json-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\261\351\023\204\231\355\215 7^\335\243\362\261\311_1\003\242X\357\366\257\236\334^\240q\000\362\262\234"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.3.0"
  }
  digests {
    sha256: "E\305\366A(@\365\r\347\277}\233\034J\214\201\352#h2Hz\0218\3132\v\214\231t\026\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose-android"
    version: "2.9.0"
  }
  digests {
    sha256: "_h\274\352\f\361\332\254S \242\375W\025r7\343\tE\317~e\206q\177\274qK5\263.o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.9.0"
  }
  digests {
    sha256: "\362e\246e\201\236Q2+>\352*o\362\253\220\030l\255\361xz\201\335\213\357\f\351<J`\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.9.0"
  }
  digests {
    sha256: "\301\\\351r\314(\223jYj\234\322V\263/\"v\212a\325\335\201\017\245k\251ER*\016\347\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-compose"
    version: "1.10.1"
  }
  digests {
    sha256: "\370\232\361\262l\314\203F48|\205|-\324\364eM7e\b\220\003\234R|/\355\a\333ja"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.0.0"
  }
  digests {
    sha256: "\311F\217V\340P\006\352\025\032BlT\225|\320y\233\213\203\245y\322\204m\322 a\363>^\315"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\210T\226v=\214\213\243/>jv[\315\265\243\236d\v\323W\177\305E!\030\321\'>,\nJ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util-android"
    version: "1.7.8"
  }
  digests {
    sha256: "V\206\252\257\374k\355\036\223\325\355\267\f\026\315*\024)o\261G\fR\366\305\246\'\261\030\214#\\"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\353Hb\3169\227\351\334\\2-\245\332kA^\342\220\177@\200wf\362\245cR\233\272\324i\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\2347\361e\2579{l\350\362\271\220\022n\210\254\332\201\341X0|\367\3403L\306\272]A\355I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text-android"
    version: "1.7.8"
  }
  digests {
    sha256: "_wW\255\027k\360\200z?\230\324\341\315\220\004\356\005\234\277\005\2422\006-\223\344\240P\327\337\272"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\341va\250\a\341\037\355.\274o\222\373Y\021\263\202N\346\241W\220\243\201\026r\b\325\267\327\376f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.graphics"
    artifactId: "graphics-path"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\244\003+my\263Q\360\265\232\324\265\200\355\333\271B>\026R\367\311X\203\006\207\361\356\342\354\003"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\002\024\037\314\351\002C\017b\266t\303*\354\320\367Rb\347\356,\321\\t\255\266\027\315\023\023\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\204E0\036\326N:\243\025\v\'\365r\205\\s#\213x$\252\301du\212\207&\267\257\337\364\201"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\251E\327{U:u\216O\376\234\223\257\342\365$5\344\227\351\262\030\256\213\342\301\353\244\177>%\234"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\2027H\210p\307>\233f\367\354\253^\301\205[\f\257\300#\260\2517\363\367\306V\002\342\310\307\262"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout-android"
    version: "1.7.8"
  }
  digests {
    sha256: "\25023\347h\343\n\253\207\016fg\'~\311\035\324\n\333Vc\323/6\337\245\333\2556}\265a"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.0.0"
  }
  digests {
    sha256: " \345\270\366Rj4YZ`OVq\215\250\021g\300\264\nz\224\245}\2525Vc\362YM\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose"
    artifactId: "compose-bom"
    version: "2024.09.00"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3"
    version: "1.3.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3-android"
    version: "1.3.0"
  }
  digests {
    sha256: "\363\033\333\202\005\207\300\231\271Fb\346\352\315B\217B\311!\036\326\fE\261\027uc\016\311\356\247\221"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\222]$c\310\vG\263\360L\253oI8\025>\017]\225\217\020\255\371B(\327\036\340n\037\304\017"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\247W8\270\3379\204\343\376\036\216I\304\265\373:\002\'\372\006 \323T\016\335\217jZw\231\307\300"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-bom"
    version: "33.14.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-analytics"
    version: "22.4.0"
  }
  digests {
    sha256: "\256\361,\0355\271$\"q>E\366\360P\036\303\024\372\221t\301\224\250}\227\361\343\336\200\222\370\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement"
    version: "22.4.0"
  }
  digests {
    sha256: "\207\312\312\260e\236\221\362\316C[I\232\006\f\375\n\200t\350\302Z\212z~\237\242\276>G\aN"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-identifier"
    version: "18.0.0"
  }
  digests {
    sha256: "s\f\233g\344\370]\2738I\364\363\344*PG\316\341\365\234#&F\235\001\331m\017\275!\030\032"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-base"
    version: "22.4.0"
  }
  digests {
    sha256: "\245@4%\257\333\231\271\267\ry\020\336~\211l\235QH\001b\207m\246\227S\227\376y\331\203\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-impl"
    version: "22.4.0"
  }
  digests {
    sha256: "\327\000*\357A\362E\351}\'f\303\246m\312\031\352\vL\375\217$l;\212~\t\300\r\322|K"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices"
    version: "1.1.0-beta11"
  }
  digests {
    sha256: "T\nn-\307\2167\006\025\367\177\207,\017;p\n\032\312\035x\244\251\257}(\b\005\bH\306c"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices-java"
    version: "1.1.0-beta11"
  }
  digests {
    sha256: "\262\210]\265\335\255\233E\177\224\322\020A\332S&}\250\232\337\3448$+\240}\321\034\217\246>t"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.5.0"
  }
  digests {
    sha256: "Y\245\300\302\332\0221\035u\331e\316\037A\224\230Sk\032\026\177\262\217\367\337\302\337\331\316\372AW"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-stats"
    version: "17.0.2"
  }
  digests {
    sha256: "\335C\024\245?I\243x\354\024a\003\323b2\271luEM)Rc6\314\275\3612\224\027d\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "31.1-android"
  }
  digests {
    sha256: "2\254.\327\t\331m\'\213].>\\\352\027\217\244\223\2319\305%\373du2\360\0230\215\263\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "3.12.0"
  }
  digests {
    sha256: "\377\020xZ\302\243W\354]\351\302\223\313\230*,\273`\\\003\t\352L\301\313\233\233\306\333\347\363\313"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.26.0"
  }
  digests {
    sha256: "S\315\374\v\353-vo\340;x\360\261\035\002\005T\315A\230y\322\003\200\303\217\241\334\362\272\033P"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.j2objc"
    artifactId: "j2objc-annotations"
    version: "1.3"
  }
  digests {
    sha256: "!\2570\311\"g\275a\"\300\340\264\322\f\314\266d\0327\352\371V\306T\016\304q\325\204\346J{"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk-api"
    version: "22.4.0"
  }
  digests {
    sha256: "\276\004\002 $\336?\273/\231\227\033\364\2144\341\324\r\226S\311T\3567\301\361@s\320\205\247\316"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-api"
    version: "22.4.0"
  }
  digests {
    sha256: "}\337\a\f\351FP\0166;\304\rV6\355\206P\256\365\232\003\251\3768|<\356\375\265\271\373\215"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common"
    version: "21.0.0"
  }
  digests {
    sha256: "7\222\207\327\027\023qQ$\223h\0339\216x\303A\2633\317\227N\350\032\030\261\325n|.8]"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "18.0.0"
  }
  digests {
    sha256: "\307\304\212:\200\364JI\236\275ds\274\374}\325\244^\365#\372\276$\024\246%\025\377<d\tr"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.2.0"
  }
  digests {
    sha256: "F\366\325\337\335,\317<@\336\211z\024\275\227y1L3\031\364K\3751\347\340\242\r\223Z^>"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common-ktx"
    version: "21.0.0"
  }
  digests {
    sha256: "%\374\200\311\273\236\313\026r\220\207\030\302\224\257\314J\301\344tsg{\340`\307\225\340O\022\000f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations"
    version: "18.0.0"
  }
  digests {
    sha256: "\225\006:\337\261\177\376I+\022\366\216s\002\bs\201M\201w\252U0i\312\326N\021\330\307\222\222"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations-interop"
    version: "17.1.1"
  }
  digests {
    sha256: "\372\306Ph\017y!\364\253\222\360\273!\240\2224\261,\332\357\253,\367\001\210(\035~\245+\215\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-measurement-connector"
    version: "19.0.0"
  }
  digests {
    sha256: "\333\247Mk\371FG\3569{\367\257\262\253\a\366\376\215\023\025~Vx_\245@\242\241>\330,\231"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk"
    version: "22.4.0"
  }
  digests {
    sha256: "\313\202\273_v\tQ\177\352\002 \3263\220\006\375]p\317\300\260\224\256\225\003\340\234\214{\002\331\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-auth"
    version: "23.2.1"
  }
  digests {
    sha256: "\300\177q\022\246b\f\225r@\241N\032\a\330[\340\001^@5d\245\3023\022\324\334N`\267\334"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.browser"
    artifactId: "browser"
    version: "1.4.0"
  }
  digests {
    sha256: "\341\220o\204Q/\036\245\344\311&\333\271\025x\232\330\367IO\244\356\232\322E\026?v\030\\\354\332"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.credentials"
    artifactId: "credentials"
    version: "1.2.0-rc01"
  }
  digests {
    sha256: "I\217e\033O\221f\277w\017l\251z\353\250\351!\356\301\001\263\337\370\331\360\305\032\310\366\344\002\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.credentials"
    artifactId: "credentials-play-services-auth"
    version: "1.2.0-rc01"
  }
  digests {
    sha256: "X0\246A8N\227\226A[0\363M\302Px-x\b\020\034\366\22264`\33199\240 \311"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth"
    version: "20.7.0"
  }
  digests {
    sha256: "\025\004\224\327\240R\253\252\254\233\270)\242\214\212\322j\303\312Y\305v\301ig\275\276(\252\252N\026"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-api-phone"
    version: "18.0.2"
  }
  digests {
    sha256: "\277\3378\016t\000\251\331P\313\244\336\334\025+\033\316aJ\20750\t\350Q\bt(\222|\302@"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-base"
    version: "18.0.4"
  }
  digests {
    sha256: "\253RK\030r]\220\243\006\r\247\\A\3241\3237\266\331\017^\2259\357\356\337!Zu\336\266?"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-fido"
    version: "20.1.0"
  }
  digests {
    sha256: "\222r:8\344\r:\312\037\2365\021YR\231\220!\251/\254$\2371a\3552\223\2214r\251\270"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.libraries.identity.googleid"
    artifactId: "googleid"
    version: "1.1.0"
  }
  digests {
    sha256: "\031J\301\374\031\206\335\037b\004o\2567\335\367~cw\017\334\037=4\272\2529|\373\364\321\221\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "integrity"
    version: "1.3.0"
  }
  digests {
    sha256: "6\253\374C\227P\270%\355\333?W\a\347\357\002\034K8\354\341\334r\202f\205\256\273\327\000L3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "core-common"
    version: "2.0.3"
  }
  digests {
    sha256: "C\003%_\025,Y\271T\221\233\264q\321N\345\376\322\337x5e\336\250R\213\b\b;\2474+"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.recaptcha"
    artifactId: "recaptcha"
    version: "18.6.1"
  }
  digests {
    sha256: "]\r\\\350i\332c\004\2754\342\027\273\367e`\034Q\306\036\305\305\177/!\233\232\210\024\321\370\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-appcheck-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\036\241\020\250\3266\3042`\241\360}zE\372\005H\210Y\372\2637;\023\004/\201\022\005\266\376<"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-auth-interop"
    version: "20.0.0"
  }
  digests {
    sha256: "\300\237\332\337\240WI\315\177]h_\367\237<$\370\273\325+\241\310\033\216k\277M\000\230\301\312I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-database"
    version: "21.0.0"
  }
  digests {
    sha256: "d\016\ag<\316F[\350;\272\270\337\327bZ9s2\212\377\246V\\\244\331#?\025\260h\247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-database-collection"
    version: "18.0.1"
  }
  digests {
    sha256: "\373\222`M\363[\370\031\347\006C/\366\343\312\235G\224\314\2054\215\224\310\207b+\251;TP\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-storage"
    version: "21.0.2"
  }
  digests {
    sha256: ".K\200X\311&\246\rIZ\374\254\037\213\000Q\320\205\351\225\334\240S\244\344\340\236}\">~\322"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-appcheck"
    version: "18.0.0"
  }
  digests {
    sha256: "\322z\363\363\034\214\316\241\351\003\f\035\303\204\021\304\v\230g?\027~\3763\344\361\265\021\360\366Y\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-compose"
    version: "2.8.5"
  }
  digests {
    sha256: ":\216t\334\311\272.<o<%\345t\347[\364\2740\317\235\236LzT\305\263\322u\350W\0033"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime-ktx"
    version: "2.8.5"
  }
  digests {
    sha256: "U\361\206!$\003\350V2\277\t\037:\276x\321kXQ\260\205m\312\2465\357\335\271Gs\366>"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common-ktx"
    version: "2.8.5"
  }
  digests {
    sha256: "\a\3000\272\"f\222\021\254\273\344T\374GV\245}\213\301\374\360\026\304\300y\230\217\205\004\a\225\217"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common"
    version: "2.8.5"
  }
  digests {
    sha256: "\345\203f\215\267\261\200\002\a\240\037\210\351u\312s\217\217\"m\372(\004\321\246{n\373:\251^\345"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime"
    version: "2.8.5"
  }
  digests {
    sha256: "\227G\267\221\370e\034\f1\034\220f\235\346`C\311\316=z\200W`o\356A\255<;\305\017c"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.webkit"
    artifactId: "webkit"
    version: "1.12.1"
  }
  digests {
    sha256: "\030\240\245\032\321)\340\310V?\003\035F\310\247\346\021i\365j\350NV*\ruhiA&!\021"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.coil-kt"
    artifactId: "coil-compose"
    version: "2.5.0"
  }
  digests {
    sha256: "\221\177@G\\\033 la\244\2115X3\341\021\346\375\347)\354\v\216<\\\312\271#\262\203d/"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.coil-kt"
    artifactId: "coil-compose-base"
    version: "2.5.0"
  }
  digests {
    sha256: "\020h\no\000\253c\006Sz&\207\"*\260\377b\260\255\203\355}\345\360)G\322\331\325\302\004\v"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.accompanist"
    artifactId: "accompanist-drawablepainter"
    version: "0.32.0"
  }
  digests {
    sha256: "h\r\'\225\017\221\272\030j\241E\"\255\001\235\255\222\307\002$\215\021\016\275\326\304\227sJ\275}\240"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.coil-kt"
    artifactId: "coil-base"
    version: "2.5.0"
  }
  digests {
    sha256: "\2739\237\2009\350\317\177\025\221\002\310[D(\033\342\377~\274X!@V\370`\034\244?\276\253\305"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.6.1"
  }
  digests {
    sha256: "\333\221]\277I5xc\336\026i\377\237\335\216\220\b\326_\343W\257l\316\232\3460C\255_f\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.6"
  }
  digests {
    sha256: "\030\004\020^\236\005\375\330\367`A;\255]\344\230\303\201\2522\237O\235\224\310Q\274\211\032\306T\306"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.12.0"
  }
  digests {
    sha256: "\261\005\000\201\261K\267\243\247\345ZM>\360\033]\317\253\304S\264W:O\300\031vq\221\325\364\340"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.6.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.6.0"
  }
  digests {
    sha256: "gT?\a6\374B*\351\'\355\016PK\230\274^&\237\332\r5\000W\2237\313q=\242\204\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.coil-kt"
    artifactId: "coil"
    version: "2.5.0"
  }
  digests {
    sha256: "\304\243\306^\301\275T0~V\356-D\177[kH\214V\361\373\373Z\020\316\301\320\3443\f\3413"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.accompanist"
    artifactId: "accompanist-permissions"
    version: "0.32.0"
  }
  digests {
    sha256: "\264\324\r\243\371\331\267\221h\271\317\r\252\a2\000\3509\356|\215x\213\351\233<\370u\356\255\302D"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.github.aakira"
    artifactId: "napier"
    version: "1.4.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.github.aakira"
    artifactId: "napier-android"
    version: "1.4.1"
  }
  digests {
    sha256: "&\234\307\264:.K]7\337)8\322u-)F\276\251\271\217\373\200\3540\257\025\003\200E\037L"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-datetime"
    version: "0.5.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-datetime-jvm"
    version: "0.5.0"
  }
  digests {
    sha256: "\277\360\323Pr\324\372\373`\200R\300\207U\227\240\303\274p>\347\225$bP\221\017\234\257\370Xc"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.dagger"
    artifactId: "hilt-android"
    version: "2.48"
  }
  digests {
    sha256: "$v\217Z\'\306\r\310\261\211G(\206\177\304\307\r\372X\243\204\331\377`\311!,\344\212!7\352"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.dagger"
    artifactId: "dagger"
    version: "2.48"
  }
  digests {
    sha256: "\037\242&\322\264\240,\310\tP\372MI\244\2425\314\216\316\324\231\265\201\3745\212UDj\203\365y"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.dagger"
    artifactId: "dagger-lint-aar"
    version: "2.48"
  }
  digests {
    sha256: "\346!\301\003\277a\264Vo?}\274\200U\253\314\356\361X\201\001\235\024\233\206z\331\355\364\a\250\253"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.dagger"
    artifactId: "hilt-core"
    version: "2.48"
  }
  digests {
    sha256: "\312\202\2453\v7%/=\336\321\345\316\005\367D\263T\253\021\266\307\344\233\332\301\b\226\305\0313a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.hilt"
    artifactId: "hilt-navigation-compose"
    version: "1.1.0"
  }
  digests {
    sha256: "\"1\324\\\331|\r\354\344L\270Z\270.r\336\275\277\366\277\244)I>\214td\235\022\031/E"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.hilt"
    artifactId: "hilt-navigation"
    version: "1.1.0"
  }
  digests {
    sha256: "F\241\321$\030\367zuO\311\034\\(\254\277\004\034x\366N\240\203\361\372\2234\235\216C\366\352\211"
  }
  repo_index {
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 8
  library_dep_index: 0
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 0
}
library_dependencies {
  library_index: 8
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 65
  library_dep_index: 95
  library_dep_index: 0
  library_dep_index: 37
  library_dep_index: 5
  library_dep_index: 0
}
library_dependencies {
  library_index: 9
  library_dep_index: 0
}
library_dependencies {
  library_index: 10
  library_dep_index: 11
}
library_dependencies {
  library_index: 11
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 12
  library_dep_index: 12
}
library_dependencies {
  library_index: 12
  library_dep_index: 10
  library_dep_index: 10
}
library_dependencies {
  library_index: 13
  library_dep_index: 6
  library_dep_index: 14
}
library_dependencies {
  library_index: 15
  library_dep_index: 0
  library_dep_index: 0
}
library_dependencies {
  library_index: 16
  library_dep_index: 6
}
library_dependencies {
  library_index: 17
  library_dep_index: 18
}
library_dependencies {
  library_index: 18
  library_dep_index: 6
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 15
  library_dep_index: 21
  library_dep_index: 83
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 37
  library_dep_index: 21
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 38
  library_dep_index: 32
  library_dep_index: 40
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 63
}
library_dependencies {
  library_index: 19
  library_dep_index: 6
}
library_dependencies {
  library_index: 20
  library_dep_index: 6
  library_dep_index: 19
}
library_dependencies {
  library_index: 21
  library_dep_index: 22
}
library_dependencies {
  library_index: 22
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 37
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 17
  library_dep_index: 38
  library_dep_index: 32
  library_dep_index: 40
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 63
}
library_dependencies {
  library_index: 23
  library_dep_index: 24
}
library_dependencies {
  library_index: 24
  library_dep_index: 1
  library_dep_index: 25
  library_dep_index: 4
  library_dep_index: 2
}
library_dependencies {
  library_index: 25
  library_dep_index: 26
  library_dep_index: 24
  library_dep_index: 23
  library_dep_index: 27
}
library_dependencies {
  library_index: 26
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 2
}
library_dependencies {
  library_index: 27
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 28
  library_dep_index: 2
}
library_dependencies {
  library_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 29
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 30
}
library_dependencies {
  library_index: 30
  library_dep_index: 31
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 5
  library_dep_index: 36
  library_dep_index: 32
  library_dep_index: 50
  library_dep_index: 92
  library_dep_index: 52
  library_dep_index: 93
  library_dep_index: 0
}
library_dependencies {
  library_index: 31
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 15
  library_dep_index: 21
  library_dep_index: 17
  library_dep_index: 32
  library_dep_index: 50
  library_dep_index: 83
  library_dep_index: 52
  library_dep_index: 65
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 66
  library_dep_index: 48
  library_dep_index: 0
}
library_dependencies {
  library_index: 32
  library_dep_index: 33
}
library_dependencies {
  library_index: 33
  library_dep_index: 6
  library_dep_index: 15
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 23
  library_dep_index: 21
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 17
  library_dep_index: 38
  library_dep_index: 40
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 63
}
library_dependencies {
  library_index: 34
  library_dep_index: 6
  library_dep_index: 21
  library_dep_index: 21
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 17
  library_dep_index: 38
  library_dep_index: 32
  library_dep_index: 40
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 63
}
library_dependencies {
  library_index: 35
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 36
  library_dep_index: 62
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 37
  library_dep_index: 21
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 62
  library_dep_index: 17
  library_dep_index: 60
  library_dep_index: 38
  library_dep_index: 32
  library_dep_index: 40
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 63
}
library_dependencies {
  library_index: 36
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 0
  library_dep_index: 37
  library_dep_index: 21
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 17
  library_dep_index: 38
  library_dep_index: 32
  library_dep_index: 40
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 63
}
library_dependencies {
  library_index: 38
  library_dep_index: 39
}
library_dependencies {
  library_index: 39
  library_dep_index: 6
  library_dep_index: 17
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 17
  library_dep_index: 32
  library_dep_index: 40
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 49
  library_dep_index: 36
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 63
}
library_dependencies {
  library_index: 40
  library_dep_index: 41
}
library_dependencies {
  library_index: 41
  library_dep_index: 6
  library_dep_index: 42
  library_dep_index: 46
  library_dep_index: 21
  library_dep_index: 32
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 54
  library_dep_index: 21
  library_dep_index: 17
  library_dep_index: 38
  library_dep_index: 32
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 36
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 63
}
library_dependencies {
  library_index: 42
  library_dep_index: 43
}
library_dependencies {
  library_index: 43
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 23
  library_dep_index: 44
}
library_dependencies {
  library_index: 44
  library_dep_index: 45
}
library_dependencies {
  library_index: 45
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 42
  library_dep_index: 0
  library_dep_index: 42
}
library_dependencies {
  library_index: 46
  library_dep_index: 47
}
library_dependencies {
  library_index: 47
  library_dep_index: 48
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 67
  library_dep_index: 10
  library_dep_index: 10
  library_dep_index: 42
  library_dep_index: 44
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 76
  library_dep_index: 74
  library_dep_index: 70
  library_dep_index: 8
  library_dep_index: 82
  library_dep_index: 78
  library_dep_index: 60
  library_dep_index: 32
  library_dep_index: 83
  library_dep_index: 59
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 23
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 76
  library_dep_index: 79
  library_dep_index: 74
  library_dep_index: 70
  library_dep_index: 84
}
library_dependencies {
  library_index: 48
  library_dep_index: 31
  library_dep_index: 38
  library_dep_index: 49
  library_dep_index: 59
  library_dep_index: 31
  library_dep_index: 66
}
library_dependencies {
  library_index: 49
  library_dep_index: 32
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 36
  library_dep_index: 17
  library_dep_index: 38
  library_dep_index: 32
  library_dep_index: 40
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 63
}
library_dependencies {
  library_index: 50
  library_dep_index: 51
}
library_dependencies {
  library_index: 51
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 36
  library_dep_index: 32
  library_dep_index: 52
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 23
  library_dep_index: 54
  library_dep_index: 21
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 17
  library_dep_index: 38
  library_dep_index: 32
  library_dep_index: 40
  library_dep_index: 49
  library_dep_index: 0
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 63
}
library_dependencies {
  library_index: 52
  library_dep_index: 53
}
library_dependencies {
  library_index: 53
  library_dep_index: 6
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 15
  library_dep_index: 21
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 54
  library_dep_index: 59
  library_dep_index: 0
}
library_dependencies {
  library_index: 54
  library_dep_index: 55
}
library_dependencies {
  library_index: 55
  library_dep_index: 56
  library_dep_index: 0
}
library_dependencies {
  library_index: 56
  library_dep_index: 55
  library_dep_index: 54
  library_dep_index: 57
  library_dep_index: 58
}
library_dependencies {
  library_index: 57
  library_dep_index: 58
}
library_dependencies {
  library_index: 58
  library_dep_index: 56
  library_dep_index: 0
  library_dep_index: 54
}
library_dependencies {
  library_index: 59
  library_dep_index: 52
  library_dep_index: 0
  library_dep_index: 52
  library_dep_index: 0
}
library_dependencies {
  library_index: 60
  library_dep_index: 61
}
library_dependencies {
  library_index: 61
  library_dep_index: 6
  library_dep_index: 42
  library_dep_index: 17
  library_dep_index: 38
  library_dep_index: 21
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 62
  library_dep_index: 17
  library_dep_index: 38
  library_dep_index: 32
  library_dep_index: 40
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 63
}
library_dependencies {
  library_index: 62
  library_dep_index: 36
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 17
  library_dep_index: 60
  library_dep_index: 38
  library_dep_index: 32
  library_dep_index: 40
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 63
}
library_dependencies {
  library_index: 63
  library_dep_index: 6
  library_dep_index: 17
  library_dep_index: 64
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 62
  library_dep_index: 17
  library_dep_index: 60
  library_dep_index: 38
  library_dep_index: 32
  library_dep_index: 40
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 0
}
library_dependencies {
  library_index: 64
  library_dep_index: 6
  library_dep_index: 65
}
library_dependencies {
  library_index: 65
  library_dep_index: 6
}
library_dependencies {
  library_index: 66
  library_dep_index: 48
  library_dep_index: 42
  library_dep_index: 44
  library_dep_index: 46
  library_dep_index: 5
  library_dep_index: 21
  library_dep_index: 17
  library_dep_index: 32
  library_dep_index: 52
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 48
  library_dep_index: 0
  library_dep_index: 31
}
library_dependencies {
  library_index: 67
  library_dep_index: 8
}
library_dependencies {
  library_index: 68
  library_dep_index: 69
}
library_dependencies {
  library_index: 69
  library_dep_index: 6
  library_dep_index: 42
  library_dep_index: 70
  library_dep_index: 0
  library_dep_index: 46
  library_dep_index: 72
  library_dep_index: 76
  library_dep_index: 79
  library_dep_index: 74
  library_dep_index: 70
}
library_dependencies {
  library_index: 70
  library_dep_index: 71
}
library_dependencies {
  library_index: 71
  library_dep_index: 9
  library_dep_index: 0
  library_dep_index: 46
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 76
  library_dep_index: 79
  library_dep_index: 74
}
library_dependencies {
  library_index: 72
  library_dep_index: 73
}
library_dependencies {
  library_index: 73
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 42
  library_dep_index: 74
  library_dep_index: 70
  library_dep_index: 8
  library_dep_index: 81
  library_dep_index: 0
  library_dep_index: 46
  library_dep_index: 68
  library_dep_index: 76
  library_dep_index: 79
  library_dep_index: 74
  library_dep_index: 70
}
library_dependencies {
  library_index: 74
  library_dep_index: 75
}
library_dependencies {
  library_index: 75
  library_dep_index: 6
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 12
  library_dep_index: 42
  library_dep_index: 68
  library_dep_index: 70
  library_dep_index: 0
  library_dep_index: 46
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 76
  library_dep_index: 79
  library_dep_index: 70
}
library_dependencies {
  library_index: 76
  library_dep_index: 77
}
library_dependencies {
  library_index: 77
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 42
  library_dep_index: 44
  library_dep_index: 72
  library_dep_index: 74
  library_dep_index: 70
  library_dep_index: 8
  library_dep_index: 78
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 46
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 79
  library_dep_index: 74
  library_dep_index: 70
}
library_dependencies {
  library_index: 78
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 63
  library_dep_index: 64
}
library_dependencies {
  library_index: 79
  library_dep_index: 80
}
library_dependencies {
  library_index: 80
  library_dep_index: 6
  library_dep_index: 42
  library_dep_index: 4
  library_dep_index: 46
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 76
  library_dep_index: 74
  library_dep_index: 70
}
library_dependencies {
  library_index: 81
  library_dep_index: 8
  library_dep_index: 0
}
library_dependencies {
  library_index: 82
  library_dep_index: 5
  library_dep_index: 0
}
library_dependencies {
  library_index: 83
  library_dep_index: 6
  library_dep_index: 13
  library_dep_index: 64
  library_dep_index: 14
}
library_dependencies {
  library_index: 84
  library_dep_index: 85
}
library_dependencies {
  library_index: 85
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 86
  library_dep_index: 90
  library_dep_index: 42
  library_dep_index: 46
  library_dep_index: 76
  library_dep_index: 70
  library_dep_index: 8
  library_dep_index: 78
  library_dep_index: 0
  library_dep_index: 90
}
library_dependencies {
  library_index: 86
  library_dep_index: 87
}
library_dependencies {
  library_index: 87
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 88
  library_dep_index: 90
  library_dep_index: 42
  library_dep_index: 46
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 70
  library_dep_index: 0
  library_dep_index: 88
}
library_dependencies {
  library_index: 88
  library_dep_index: 89
}
library_dependencies {
  library_index: 89
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 42
  library_dep_index: 46
  library_dep_index: 72
  library_dep_index: 74
  library_dep_index: 70
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 86
}
library_dependencies {
  library_index: 90
  library_dep_index: 91
}
library_dependencies {
  library_index: 91
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 88
  library_dep_index: 42
  library_dep_index: 46
  library_dep_index: 74
  library_dep_index: 70
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 84
}
library_dependencies {
  library_index: 92
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 35
  library_dep_index: 32
}
library_dependencies {
  library_index: 93
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 94
}
library_dependencies {
  library_index: 94
  library_dep_index: 6
  library_dep_index: 8
}
library_dependencies {
  library_index: 95
  library_dep_index: 6
  library_dep_index: 10
}
library_dependencies {
  library_index: 96
  library_dep_index: 97
  library_dep_index: 42
  library_dep_index: 44
  library_dep_index: 46
  library_dep_index: 72
  library_dep_index: 79
  library_dep_index: 86
  library_dep_index: 90
  library_dep_index: 84
  library_dep_index: 98
  library_dep_index: 88
  library_dep_index: 99
  library_dep_index: 101
  library_dep_index: 76
  library_dep_index: 70
  library_dep_index: 68
  library_dep_index: 74
  library_dep_index: 43
  library_dep_index: 47
  library_dep_index: 100
  library_dep_index: 102
  library_dep_index: 45
  library_dep_index: 69
  library_dep_index: 73
  library_dep_index: 77
  library_dep_index: 75
  library_dep_index: 71
  library_dep_index: 80
  library_dep_index: 85
  library_dep_index: 87
  library_dep_index: 91
  library_dep_index: 89
}
library_dependencies {
  library_index: 97
  library_dep_index: 98
}
library_dependencies {
  library_index: 98
  library_dep_index: 66
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 88
  library_dep_index: 84
  library_dep_index: 90
  library_dep_index: 99
  library_dep_index: 101
  library_dep_index: 42
  library_dep_index: 46
  library_dep_index: 76
  library_dep_index: 70
  library_dep_index: 34
  library_dep_index: 4
}
library_dependencies {
  library_index: 99
  library_dep_index: 100
}
library_dependencies {
  library_index: 100
  library_dep_index: 46
  library_dep_index: 0
  library_dep_index: 4
}
library_dependencies {
  library_index: 101
  library_dep_index: 102
}
library_dependencies {
  library_index: 102
  library_dep_index: 10
  library_dep_index: 86
  library_dep_index: 84
  library_dep_index: 42
  library_dep_index: 70
  library_dep_index: 4
}
library_dependencies {
  library_index: 103
  library_dep_index: 104
  library_dep_index: 134
  library_dep_index: 148
  library_dep_index: 150
  library_dep_index: 125
  library_dep_index: 129
  library_dep_index: 151
  library_dep_index: 130
}
library_dependencies {
  library_index: 104
  library_dep_index: 105
  library_dep_index: 124
  library_dep_index: 133
}
library_dependencies {
  library_index: 105
  library_dep_index: 10
  library_dep_index: 106
  library_dep_index: 110
  library_dep_index: 29
  library_dep_index: 111
  library_dep_index: 112
  library_dep_index: 123
  library_dep_index: 116
}
library_dependencies {
  library_index: 106
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 107
  library_dep_index: 92
  library_dep_index: 108
  library_dep_index: 109
}
library_dependencies {
  library_index: 107
  library_dep_index: 6
}
library_dependencies {
  library_index: 108
  library_dep_index: 6
}
library_dependencies {
  library_index: 109
  library_dep_index: 6
}
library_dependencies {
  library_index: 110
  library_dep_index: 29
}
library_dependencies {
  library_index: 111
  library_dep_index: 29
}
library_dependencies {
  library_index: 112
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 113
  library_dep_index: 114
  library_dep_index: 110
  library_dep_index: 115
  library_dep_index: 29
  library_dep_index: 111
  library_dep_index: 116
  library_dep_index: 28
  library_dep_index: 117
}
library_dependencies {
  library_index: 113
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 114
}
library_dependencies {
  library_index: 114
  library_dep_index: 6
  library_dep_index: 13
  library_dep_index: 5
  library_dep_index: 113
  library_dep_index: 14
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 113
}
library_dependencies {
  library_index: 115
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 28
}
library_dependencies {
  library_index: 116
  library_dep_index: 106
  library_dep_index: 29
}
library_dependencies {
  library_index: 117
  library_dep_index: 118
  library_dep_index: 14
  library_dep_index: 119
  library_dep_index: 120
  library_dep_index: 121
  library_dep_index: 122
}
library_dependencies {
  library_index: 123
  library_dep_index: 29
  library_dep_index: 111
}
library_dependencies {
  library_index: 124
  library_dep_index: 110
  library_dep_index: 29
  library_dep_index: 111
  library_dep_index: 123
  library_dep_index: 28
  library_dep_index: 125
  library_dep_index: 129
  library_dep_index: 126
  library_dep_index: 130
  library_dep_index: 131
  library_dep_index: 132
  library_dep_index: 117
  library_dep_index: 0
}
library_dependencies {
  library_index: 125
  library_dep_index: 27
  library_dep_index: 126
  library_dep_index: 127
  library_dep_index: 6
  library_dep_index: 13
  library_dep_index: 0
  library_dep_index: 29
  library_dep_index: 28
}
library_dependencies {
  library_index: 126
  library_dep_index: 127
  library_dep_index: 6
  library_dep_index: 121
}
library_dependencies {
  library_index: 127
  library_dep_index: 128
}
library_dependencies {
  library_index: 129
  library_dep_index: 125
  library_dep_index: 2
  library_dep_index: 126
  library_dep_index: 127
}
library_dependencies {
  library_index: 130
  library_dep_index: 28
  library_dep_index: 127
  library_dep_index: 125
  library_dep_index: 129
  library_dep_index: 126
  library_dep_index: 131
  library_dep_index: 0
}
library_dependencies {
  library_index: 131
  library_dep_index: 28
  library_dep_index: 127
}
library_dependencies {
  library_index: 132
  library_dep_index: 29
  library_dep_index: 127
}
library_dependencies {
  library_index: 133
  library_dep_index: 10
  library_dep_index: 29
  library_dep_index: 111
  library_dep_index: 112
}
library_dependencies {
  library_index: 134
  library_dep_index: 135
  library_dep_index: 10
  library_dep_index: 136
  library_dep_index: 137
  library_dep_index: 30
  library_dep_index: 108
  library_dep_index: 139
  library_dep_index: 29
  library_dep_index: 28
  library_dep_index: 143
  library_dep_index: 145
  library_dep_index: 127
  library_dep_index: 146
  library_dep_index: 147
  library_dep_index: 125
  library_dep_index: 129
  library_dep_index: 126
  library_dep_index: 0
}
library_dependencies {
  library_index: 135
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 16
  library_dep_index: 8
  library_dep_index: 6
  library_dep_index: 14
}
library_dependencies {
  library_index: 136
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 137
}
library_dependencies {
  library_index: 137
  library_dep_index: 136
  library_dep_index: 138
  library_dep_index: 141
  library_dep_index: 142
  library_dep_index: 0
  library_dep_index: 136
}
library_dependencies {
  library_index: 138
  library_dep_index: 30
  library_dep_index: 139
  library_dep_index: 140
  library_dep_index: 115
  library_dep_index: 29
  library_dep_index: 141
  library_dep_index: 28
}
library_dependencies {
  library_index: 139
  library_dep_index: 115
  library_dep_index: 29
  library_dep_index: 28
}
library_dependencies {
  library_index: 140
  library_dep_index: 10
  library_dep_index: 115
  library_dep_index: 29
  library_dep_index: 28
}
library_dependencies {
  library_index: 141
  library_dep_index: 115
  library_dep_index: 29
  library_dep_index: 28
}
library_dependencies {
  library_index: 142
  library_dep_index: 0
  library_dep_index: 2
}
library_dependencies {
  library_index: 143
  library_dep_index: 29
  library_dep_index: 28
  library_dep_index: 144
}
library_dependencies {
  library_index: 145
  library_dep_index: 29
  library_dep_index: 28
  library_dep_index: 143
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 27
}
library_dependencies {
  library_index: 146
  library_dep_index: 115
  library_dep_index: 28
}
library_dependencies {
  library_index: 147
  library_dep_index: 29
  library_dep_index: 28
  library_dep_index: 127
  library_dep_index: 125
}
library_dependencies {
  library_index: 148
  library_dep_index: 146
  library_dep_index: 125
  library_dep_index: 129
  library_dep_index: 126
  library_dep_index: 147
  library_dep_index: 149
  library_dep_index: 28
  library_dep_index: 6
  library_dep_index: 115
  library_dep_index: 29
  library_dep_index: 0
  library_dep_index: 23
}
library_dependencies {
  library_index: 149
  library_dep_index: 115
}
library_dependencies {
  library_index: 150
  library_dep_index: 127
  library_dep_index: 151
  library_dep_index: 146
  library_dep_index: 147
  library_dep_index: 125
  library_dep_index: 129
  library_dep_index: 126
  library_dep_index: 6
  library_dep_index: 115
  library_dep_index: 28
  library_dep_index: 0
  library_dep_index: 23
}
library_dependencies {
  library_index: 151
  library_dep_index: 28
  library_dep_index: 146
  library_dep_index: 129
  library_dep_index: 6
  library_dep_index: 115
  library_dep_index: 0
}
library_dependencies {
  library_index: 152
  library_dep_index: 66
  library_dep_index: 86
  library_dep_index: 90
  library_dep_index: 42
  library_dep_index: 44
  library_dep_index: 46
  library_dep_index: 40
  library_dep_index: 153
  library_dep_index: 0
  library_dep_index: 54
  library_dep_index: 153
  library_dep_index: 154
  library_dep_index: 156
  library_dep_index: 155
}
library_dependencies {
  library_index: 153
  library_dep_index: 154
  library_dep_index: 156
  library_dep_index: 154
  library_dep_index: 152
  library_dep_index: 156
  library_dep_index: 155
}
library_dependencies {
  library_index: 154
  library_dep_index: 155
  library_dep_index: 155
  library_dep_index: 152
  library_dep_index: 156
  library_dep_index: 153
}
library_dependencies {
  library_index: 155
  library_dep_index: 6
  library_dep_index: 12
  library_dep_index: 5
  library_dep_index: 21
  library_dep_index: 38
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 83
  library_dep_index: 59
  library_dep_index: 0
  library_dep_index: 54
  library_dep_index: 154
  library_dep_index: 152
  library_dep_index: 156
  library_dep_index: 153
}
library_dependencies {
  library_index: 156
  library_dep_index: 48
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 38
  library_dep_index: 49
  library_dep_index: 155
  library_dep_index: 0
  library_dep_index: 54
  library_dep_index: 155
  library_dep_index: 154
  library_dep_index: 152
  library_dep_index: 153
}
library_dependencies {
  library_index: 157
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 8
}
library_dependencies {
  library_index: 158
  library_dep_index: 159
  library_dep_index: 169
  library_dep_index: 2
}
library_dependencies {
  library_index: 159
  library_dep_index: 5
  library_dep_index: 160
  library_dep_index: 161
  library_dep_index: 84
  library_dep_index: 2
}
library_dependencies {
  library_index: 160
  library_dep_index: 46
  library_dep_index: 26
  library_dep_index: 2
}
library_dependencies {
  library_index: 161
  library_dep_index: 6
  library_dep_index: 162
  library_dep_index: 10
  library_dep_index: 5
  library_dep_index: 165
  library_dep_index: 83
  library_dep_index: 17
  library_dep_index: 26
  library_dep_index: 0
  library_dep_index: 166
  library_dep_index: 167
}
library_dependencies {
  library_index: 162
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 163
  library_dep_index: 164
}
library_dependencies {
  library_index: 163
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 164
  library_dep_index: 163
  library_dep_index: 16
  library_dep_index: 10
}
library_dependencies {
  library_index: 165
  library_dep_index: 6
}
library_dependencies {
  library_index: 166
  library_dep_index: 167
  library_dep_index: 2
}
library_dependencies {
  library_index: 167
  library_dep_index: 168
}
library_dependencies {
  library_index: 168
  library_dep_index: 2
  library_dep_index: 4
}
library_dependencies {
  library_index: 169
  library_dep_index: 161
  library_dep_index: 2
}
library_dependencies {
  library_index: 170
  library_dep_index: 66
  library_dep_index: 84
  library_dep_index: 26
  library_dep_index: 171
  library_dep_index: 2
}
library_dependencies {
  library_index: 171
  library_dep_index: 172
}
library_dependencies {
  library_index: 172
  library_dep_index: 0
  library_dep_index: 4
}
library_dependencies {
  library_index: 173
  library_dep_index: 174
}
library_dependencies {
  library_index: 174
  library_dep_index: 0
}
library_dependencies {
  library_index: 175
  library_dep_index: 176
  library_dep_index: 177
  library_dep_index: 178
  library_dep_index: 119
  library_dep_index: 31
  library_dep_index: 6
  library_dep_index: 30
  library_dep_index: 21
  library_dep_index: 32
  library_dep_index: 50
  library_dep_index: 52
  library_dep_index: 128
  library_dep_index: 0
}
library_dependencies {
  library_index: 176
  library_dep_index: 128
}
library_dependencies {
  library_index: 178
  library_dep_index: 176
  library_dep_index: 119
  library_dep_index: 128
}
library_dependencies {
  library_index: 179
  library_dep_index: 42
  library_dep_index: 46
  library_dep_index: 180
  library_dep_index: 40
  library_dep_index: 152
  library_dep_index: 0
}
library_dependencies {
  library_index: 180
  library_dep_index: 6
  library_dep_index: 156
  library_dep_index: 175
  library_dep_index: 0
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 38
  dependency_index: 66
  dependency_index: 96
  dependency_index: 46
  dependency_index: 72
  dependency_index: 79
  dependency_index: 97
  dependency_index: 103
  dependency_index: 104
  dependency_index: 148
  dependency_index: 134
  dependency_index: 150
  dependency_index: 152
  dependency_index: 157
  dependency_index: 40
  dependency_index: 26
  dependency_index: 158
  dependency_index: 170
  dependency_index: 173
  dependency_index: 57
  dependency_index: 175
  dependency_index: 179
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
