@echo off
echo Fixing ALL icon references to basic ones...

REM Replace ALL problematic icons with basic available ones
powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\components\BottomNavigationBar.kt') -replace 'Icons.Default.Message', 'Icons.Default.Email' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\components\BottomNavigationBar.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\components\MessageBubble.kt') -replace 'Icons.Default.Image', 'Icons.Default.Photo' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\components\MessageBubble.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\components\MessageBubble.kt') -replace 'Icons.Default.AttachFile', 'Icons.Default.Add' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\components\MessageBubble.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\components\RoomCard.kt') -replace 'Icons.Default.Chat', 'Icons.Default.Email' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\components\RoomCard.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\components\RoomCard.kt') -replace 'Icons.Default.People', 'Icons.Default.Person' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\components\RoomCard.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\AdminScreens.kt') -replace 'Icons.Default.Block', 'Icons.Default.Close' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\AdminScreens.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\ChatScreen.kt') -replace 'Icons.Default.AttachFile', 'Icons.Default.Add' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\ChatScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\HomeScreen.kt') -replace 'Icons.Default.Message', 'Icons.Default.Email' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\HomeScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\HomeScreen.kt') -replace 'Icons.Default.Chat', 'Icons.Default.Email' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\HomeScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\NotificationsScreen.kt') -replace 'Icons.Default.Message', 'Icons.Default.Email' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\NotificationsScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\PrivateChatScreen.kt') -replace 'Icons.Default.VideoCall', 'Icons.Default.Call' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\PrivateChatScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\PrivateChatScreen.kt') -replace 'Icons.Default.Attachment', 'Icons.Default.Add' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\PrivateChatScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\PrivateChatsScreen.kt') -replace 'Icons.Default.Message', 'Icons.Default.Email' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\PrivateChatsScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt') -replace 'Icons.Default.Language', 'Icons.Default.Settings' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt') -replace 'Icons.Default.Security', 'Icons.Default.Lock' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt') -replace 'Icons.Default.Palette', 'Icons.Default.Settings' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt') -replace 'Icons.Default.Storage', 'Icons.Default.Settings' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt') -replace 'Icons.Default.HelpOutline', 'Icons.Default.Help' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt'"

powershell -Command "(Get-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt') -replace 'Icons.Default.Description', 'Icons.Default.Info' | Set-Content 'app\src\main\java\com\trustmarket\trust\ui\screens\SettingsScreen.kt'"

echo All icons fixed to basic ones!
