<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\trust\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\trust\app\src\main\res"><file name="ic_launcher_background" path="C:\Users\<USER>\Desktop\trust\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\trust\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\trust\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\trust\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\trust\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\trust\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\trust\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\trust\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\trust\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\trust\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\trust\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\trust\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\trust\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\trust\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Desktop\trust\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="C:\Users\<USER>\Desktop\trust\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Trust Market</string><string name="sign_in_title">Trust Market</string><string name="sign_in_subtitle">منصة بيع وشراء بشكل آمن</string><string name="sign_in_welcome">مرحباً بك في Trust Market</string><string name="sign_in_description">تطبيق دردشة متطور للتواصل الآمن والفعال</string><string name="sign_in_with_google">تسجيل الدخول بحساب Google</string><string name="chat_rooms">غرف الدردشة</string><string name="no_rooms">لا توجد غرف دردشة</string><string name="create_room_hint">ابدأ بإنشاء غرفة جديدة</string><string name="create_room">إنشاء غرفة جديدة</string><string name="private_chats">الدردشات الخاصة</string><string name="settings">الإعدادات</string><string name="sign_out">تسجيل الخروج</string><string name="users_online">%d مستخدم متصل</string><string name="type_message">اكتب رسالة...</string><string name="send">إرسال</string><string name="attach_file">إرفاق ملف</string><string name="room_info">معلومات الغرفة</string><string name="back">العودة</string><string name="image_message">صورة</string><string name="audio_message">رسالة صوتية</string><string name="like">إعجاب</string><string name="delete">حذف</string><string name="room_name">اسم الغرفة</string><string name="room_description">وصف الغرفة (اختياري)</string><string name="create">إنشاء</string><string name="cancel">إلغاء</string><string name="features">المميزات:</string><string name="feature_group_chat">دردشة جماعية في الوقت الفعلي</string><string name="feature_private_chat">دردشة خاصة آمنة</string><string name="feature_file_sharing">مشاركة الملفات والصور</string><string name="feature_notifications">نظام إشعارات متطور</string><string name="feature_easy_ui">واجهة سهلة الاستخدام</string></file><file path="C:\Users\<USER>\Desktop\trust\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.Trust" parent="android:Theme.Material.Light.NoActionBar"/></file><file name="backup_rules" path="C:\Users\<USER>\Desktop\trust\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Desktop\trust\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\trust\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\trust\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\trust\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\Desktop\trust\app\build\generated\res\processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\trust\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\Desktop\trust\app\build\generated\res\processDebugGoogleServices"><file path="C:\Users\<USER>\Desktop\trust\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="default_web_client_id" translatable="false">300804286264-d7r4cm9r03l54b1mhim74nfh7cgdjngm.apps.googleusercontent.com</string><string name="firebase_database_url" translatable="false">https://toika-369-default-rtdb.firebaseio.com</string><string name="gcm_defaultSenderId" translatable="false">300804286264</string><string name="google_api_key" translatable="false">AIzaSyDfX9GNdA-ahOMeJfaPYbS1bfmwd2O8FbI</string><string name="google_app_id" translatable="false">1:300804286264:android:599f78b63fdb07745403cd</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyDfX9GNdA-ahOMeJfaPYbS1bfmwd2O8FbI</string><string name="google_storage_bucket" translatable="false">toika-369.appspot.com</string><string name="project_id" translatable="false">toika-369</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>