package com.trustmarket.trust.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import com.trustmarket.trust.data.models.Message
import com.trustmarket.trust.data.models.User
import com.trustmarket.trust.ui.components.MessageBubble
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PrivateChatScreen(
    userId: String,
    onBackClick: () -> Unit
) {
    var messageText by remember { mutableStateOf("") }
    val listState = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()
    
    // Mock data - replace with real data from ViewModel
    val otherUser = remember {
        User(
            uid = userId,
            name = "أحمد محمد",
            email = "<EMAIL>",
            avatar = "",
            isOnline = true,
            lastOnline = System.currentTimeMillis()
        )
    }
    
    val messages = remember {
        listOf(
            Message(
                id = "1",
                text = "مرحباً، كيف حالك؟",
                authorId = userId,
                authorName = "أحمد محمد",
                timestamp = System.currentTimeMillis() - 600000,
                isPrivate = true,
                recipientId = "currentUser"
            ),
            Message(
                id = "2",
                text = "أهلاً وسهلاً، بخير والحمد لله",
                authorId = "currentUser",
                authorName = "أنا",
                timestamp = System.currentTimeMillis() - 300000,
                isPrivate = true,
                recipientId = userId
            ),
            Message(
                id = "3",
                text = "هل يمكنك مساعدتي في شيء؟",
                authorId = userId,
                authorName = "أحمد محمد",
                timestamp = System.currentTimeMillis() - 60000,
                isPrivate = true,
                recipientId = "currentUser"
            )
        )
    }
    
    // Auto scroll to bottom when new messages arrive
    LaunchedEffect(messages.size) {
        if (messages.isNotEmpty()) {
            coroutineScope.launch {
                listState.animateScrollToItem(messages.size - 1)
            }
        }
    }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    AsyncImage(
                        model = otherUser.avatar.ifEmpty { "https://via.placeholder.com/40" },
                        contentDescription = "صورة ${otherUser.name}",
                        modifier = Modifier
                            .size(40.dp)
                            .clip(CircleShape),
                        contentScale = ContentScale.Crop
                    )
                    
                    Spacer(modifier = Modifier.width(12.dp))
                    
                    Column {
                        Text(
                            text = otherUser.name,
                            fontWeight = FontWeight.Bold,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            fontSize = 16.sp
                        )
                        
                        Text(
                            text = if (otherUser.isOnline) "متصل الآن" else "غير متصل",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.8f)
                        )
                    }
                }
            },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "العودة"
                    )
                }
            },
            actions = {
                IconButton(onClick = { /* Video call */ }) {
                    Icon(
                        imageVector = Icons.Default.VideoCall,
                        contentDescription = "مكالمة فيديو"
                    )
                }
                
                IconButton(onClick = { /* Voice call */ }) {
                    Icon(
                        imageVector = Icons.Default.Call,
                        contentDescription = "مكالمة صوتية"
                    )
                }
                
                IconButton(onClick = { /* More options */ }) {
                    Icon(
                        imageVector = Icons.Default.MoreVert,
                        contentDescription = "المزيد"
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = MaterialTheme.colorScheme.primary,
                titleContentColor = MaterialTheme.colorScheme.onPrimary,
                navigationIconContentColor = MaterialTheme.colorScheme.onPrimary,
                actionIconContentColor = MaterialTheme.colorScheme.onPrimary
            )
        )
        
        // Messages List
        LazyColumn(
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth(),
            state = listState,
            contentPadding = PaddingValues(horizontal = 8.dp, vertical = 8.dp),
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            items(messages) { message ->
                MessageBubble(
                    message = message,
                    isCurrentUser = message.authorId == "currentUser",
                    onLikeClick = { /* Handle like */ },
                    onDeleteClick = { /* Handle delete */ }
                )
            }
        }
        
        // Message Input
        Surface(
            modifier = Modifier.fillMaxWidth(),
            color = MaterialTheme.colorScheme.surface,
            shadowElevation = 8.dp
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(8.dp),
                verticalAlignment = Alignment.Bottom
            ) {
                // Attach button
                IconButton(
                    onClick = { /* Attach file */ },
                    modifier = Modifier.size(40.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Attachment,
                        contentDescription = "إرفاق ملف",
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
                
                // Message input field
                OutlinedTextField(
                    value = messageText,
                    onValueChange = { messageText = it },
                    modifier = Modifier
                        .weight(1f)
                        .padding(horizontal = 8.dp),
                    placeholder = {
                        Text(
                            text = "اكتب رسالة خاصة...",
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                        )
                    },
                    shape = RoundedCornerShape(20.dp),
                    maxLines = 4,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = MaterialTheme.colorScheme.primary,
                        unfocusedBorderColor = MaterialTheme.colorScheme.outline.copy(alpha = 0.5f)
                    )
                )
                
                // Send button
                IconButton(
                    onClick = {
                        if (messageText.isNotBlank()) {
                            // Send message
                            messageText = ""
                        }
                    },
                    modifier = Modifier.size(40.dp),
                    enabled = messageText.isNotBlank()
                ) {
                    Icon(
                        imageVector = Icons.Default.Send,
                        contentDescription = "إرسال",
                        tint = if (messageText.isNotBlank()) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.3f)
                        }
                    )
                }
            }
        }
    }
}
